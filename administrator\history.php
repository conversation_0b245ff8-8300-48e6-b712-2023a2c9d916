<?php
require_once 'includes/auth.php';
requireAdminLogin();

require_once dirname(__DIR__) . '/config/database.php';

$current_admin = getCurrentAdmin();

try {
    $pdo = getDBConnection();
    
    // Get order history with order and admin details
    $query = "
        SELECT 
            oh.*,
            o.order_number,
            o.customer_name,
            o.title,
            a.username as changed_by_username
        FROM order_history oh
        JOIN orders o ON oh.order_id = o.id
        LEFT JOIN admins a ON oh.changed_by = a.id
        ORDER BY oh.changed_at DESC
    ";
    
    $stmt = $pdo->query($query);
    $history = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = 'Database error occurred.';
    $history = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>History - Borges Media Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <h1>Borges Media Admin</h1>
                <div class="header-user">
                    Welcome, <?php echo htmlspecialchars($current_admin['username']); ?>
                </div>
            </div>
        </header>
        
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="orders.php">List of Orders</a></li>
                    <li><a href="history.php" class="active">History</a></li>
                    <li><a href="?logout=1" class="logout-btn">Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <div class="main-content">
                <h2>Order History</h2>
                
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-error"><?php echo $error_message; ?></div>
                <?php endif; ?>
                
                <!-- History Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date/Time</th>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Title</th>
                                <th>Status Change</th>
                                <th>Changed By</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($history)): ?>
                                <?php foreach ($history as $record): ?>
                                    <tr>
                                        <td><?php echo date('M j, Y g:i A', strtotime($record['changed_at'])); ?></td>
                                        <td><?php echo htmlspecialchars($record['order_number']); ?></td>
                                        <td><?php echo htmlspecialchars($record['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($record['title']); ?></td>
                                        <td>
                                            <?php if ($record['status_from']): ?>
                                                <span class="status status-<?php echo $record['status_from']; ?>">
                                                    <?php echo ucfirst(str_replace('_', ' ', $record['status_from'])); ?>
                                                </span>
                                                →
                                            <?php endif; ?>
                                            <span class="status status-<?php echo $record['status_to']; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $record['status_to'])); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($record['changed_by_username'] ?? 'System'); ?></td>
                                        <td><?php echo htmlspecialchars($record['notes'] ?? ''); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No history records found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Summary Stats -->
                <div class="history-stats">
                    <h3>History Summary</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h4>Total Changes</h4>
                            <div class="stat-number"><?php echo count($history); ?></div>
                        </div>
                        <div class="stat-card">
                            <h4>Recent Activity</h4>
                            <div class="stat-number">
                                <?php 
                                $recent_count = 0;
                                foreach ($history as $record) {
                                    if (strtotime($record['changed_at']) > strtotime('-24 hours')) {
                                        $recent_count++;
                                    }
                                }
                                echo $recent_count;
                                ?>
                            </div>
                            <small>Last 24 hours</small>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/admin-script.js"></script>
</body>
</html>
