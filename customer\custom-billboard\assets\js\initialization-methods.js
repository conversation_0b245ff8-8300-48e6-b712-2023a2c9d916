// ========================================
// INITIALIZATION METHODS
// ========================================

// Extend CF7TextEditor class with initialization methods
Object.assign(CF7TextEditor.prototype, {
    init() {
        this.setupCanvas();
        this.setupToolbar();
        this.setupEventListeners();

        // Initialize with disabled font controls - CF7 Pattern
        this.disableFontControls();
    },

    setupCanvas() {
        const width = this.canvas.dataset.width || 800;
        const height = this.canvas.dataset.height || 400;
        // Billboard aspect ratio is handled by CSS, but we set max dimensions
        this.canvas.style.maxWidth = width + 'px';
        this.canvas.style.maxHeight = height + 'px';
    },

    setupToolbar() {
        // Convert CF7 shortcodes to functional buttons
        this.convertShortcodeToButton('[cf7-add-text', 'cf7-btn-text', () => this.addTextElement());
        this.convertShortcodeToButton('[cf7-add-image', 'cf7-btn-image', () => this.triggerImageUpload());
        this.convertShortcodeToButton('[cf7-export-canvas', 'cf7-btn-export', () => this.exportCanvasAsPNG());
        this.convertShortcodeToButton('[cf7-clear-canvas', 'cf7-btn-clear', () => this.clearCanvas());

        // Setup background controls
        this.setupBackgroundControls();

        // Setup font controls
        this.setupFontControls();
    },

    setupEventListeners() {
        // Global mouse events for drag and resize
        document.addEventListener('mousemove', (e) => {
            this.handleMouseMove(e);
            this.handleResizeMove(e);
        });

        document.addEventListener('mouseup', () => {
            this.handleMouseUp();
            this.handleResizeUp();
        });

        // Canvas click to deselect
        this.canvas.addEventListener('click', (e) => {
            if (e.target === this.canvas || e.target === this.elementsContainer) {
                this.deselectAll();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Delete' && this.selectedElement) {
                this.deleteElement(this.selectedElement);
            }
        });
    }
});