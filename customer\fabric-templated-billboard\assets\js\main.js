/**
 * main.js - Application initialization and coordination
 * Initializes all modules and coordinates their interactions
 */

class BillboardEditor {
    constructor() {
        this.modules = {};
        this.isInitialized = false;
        this.config = {
            canvasId: 'billboard-canvas',
            canvasWidth: 800,
            canvasHeight: 400
        };
        
        this.init();
    }

    /**
     * Initialize the billboard editor application
     */
    async init() {
        try {
            console.log('Initializing Billboard Editor...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeModules());
            } else {
                this.initializeModules();
            }
            
        } catch (error) {
            console.error('Failed to initialize Billboard Editor:', error);
            this.showError('Failed to initialize editor. Please refresh the page.');
        }
    }

    /**
     * Initialize all modules in the correct order
     */
    initializeModules() {
        try {
            console.log('🔄 Starting module initialization...');

            // 1. Initialize responsive manager first
            console.log('🔄 Initializing ResponsiveManager...');
            this.modules.responsive = new ResponsiveManager();
            console.log('✅ ResponsiveManager initialized');

            // 2. Initialize canvas manager
            console.log('🔄 Initializing CanvasManager...');
            this.modules.canvas = new CanvasManager(this.config.canvasId, {
                width: this.config.canvasWidth,
                height: this.config.canvasHeight
            });
            console.log('✅ CanvasManager initialized');

            // 3. Initialize notification manager
            console.log('🔄 Initializing NotificationManager...');
            this.modules.notification = new NotificationManager();
            console.log('✅ NotificationManager initialized');

            // 4. Initialize UI manager
            console.log('🔄 Initializing UIManager...');
            this.modules.ui = new UIManager(this.modules.responsive, this.modules.notification);
            console.log('✅ UIManager initialized');

            // 5. Initialize image replacement manager
            console.log('🔄 Initializing ImageReplacementManager...');
            this.modules.imageReplacement = new ImageReplacementManager(this.modules.canvas);
            console.log('✅ ImageReplacementManager initialized');

            // 6. Initialize event handler
            console.log('🔄 Initializing EventHandler...');
            this.modules.events = new EventHandler(this.modules.canvas);
            console.log('✅ EventHandler initialized');

            // 7. Initialize template manager (using existing one)
            console.log('🔄 Checking TemplateManager...');
            if (!window.templateManager) {
                throw new Error('TemplateManager not found. Make sure template-manager.js is loaded.');
            }
            this.modules.templates = window.templateManager;
            console.log('✅ TemplateManager connected');

            // 8. Initialize background manager
            console.log('🔄 Initializing BackgroundManager...');
            this.modules.background = new BackgroundManager(this.modules.canvas, this.modules.templates);
            console.log('✅ BackgroundManager initialized');

            // 9. Initialize template integration manager
            console.log('🔄 Initializing TemplateIntegrationManager...');
            this.modules.templateIntegration = new TemplateIntegrationManager(
                this.modules.canvas,
                this.modules.templates,
                this.modules.background,
                this.modules.imageReplacement,
                this.modules.notification
            );
            console.log('✅ TemplateIntegrationManager initialized');

            // 10. Initialize text customization manager
            console.log('🔄 Initializing TextCustomizationManager...');
            this.modules.textCustomization = new TextCustomizationManager(
                this.modules.canvas,
                this.modules.notification
            );
            console.log('✅ TextCustomizationManager initialized');

            // 11. Initialize image upload manager
            console.log('🔄 Initializing ImageUploadManager...');
            this.modules.imageUpload = new ImageUploadManager(
                this.modules.canvas,
                this.modules.notification
            );
            console.log('✅ ImageUploadManager initialized');

            // 12. Initialize export manager
            console.log('🔄 Initializing ExportManager...');
            this.modules.export = new ExportManager(
                this.modules.canvas,
                this.modules.notification
            );
            console.log('✅ ExportManager initialized');

            // 13. Initialize event coordinator (last - coordinates all modules)
            console.log('🔄 Initializing EventCoordinator...');
            this.modules.eventCoordinator = new EventCoordinator(this.modules);
            console.log('✅ EventCoordinator initialized');

            this.isInitialized = true;
            console.log('🎉 Billboard Editor initialized successfully');

            // Emit ready event
            this.emit('editor:ready');

        } catch (error) {
            console.error('❌ Error initializing modules:', error);
            this.modules.notification?.showError(`Error initializing editor: ${error.message}`) ||
                this.showError(`Error initializing editor: ${error.message}`);
        }
    }

    /**
     * Legacy method - now handled by EventCoordinator
     * @deprecated Use EventCoordinator instead
     */
    setupModuleCommunication() {
        console.log('ℹ️ Module communication now handled by EventCoordinator');
    }

    /**
     * Legacy method - now handled by EventCoordinator
     * @deprecated Use EventCoordinator instead
     */
    setupUIHandlers() {
        console.log('ℹ️ UI event handlers now handled by EventCoordinator');
    }

    /**
     * Legacy method - now handled by UIManager
     * @deprecated Use UIManager instead
     */
    initializeUI() {
        console.log('ℹ️ UI initialization now handled by UIManager');
    }

    /**
     * Fallback error handler for legacy methods
     */
    showError(message) {
        console.error('Billboard Editor Error:', message);
        alert(message); // Fallback for when NotificationManager isn't available
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Get module instance
     */
    getModule(name) {
        return this.modules[name];
    }

    /**
     * Check if editor is initialized
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy editor and clean up
     */
    destroy() {
        Object.values(this.modules).forEach(module => {
            if (module && typeof module.destroy === 'function') {
                module.destroy();
            }
        });
        this.modules = {};
        this.isInitialized = false;
        console.log('🗑️ Billboard Editor destroyed');
    }
}

// Initialize the billboard editor when the script loads
window.billboardEditor = new BillboardEditor();