<?php
// High-Quality Post-Payment Image Generator
// Generates high-quality billboard images after successful payment using captured design data

require_once dirname(__DIR__, 2) . '/config/database.php';
require_once dirname(__DIR__, 2) . '/config/storage.php';

class HighQualityPostPaymentGenerator {
    private $pdo;
    private $orderId;
    private $order;
    private $designData;
    
    // High-quality settings matching frontend system
    private $qualitySettings = [
        'width' => 3200,           // 4x base width (800)
        'height' => 1600,          // 4x base height (400)
        'quality' => 98,           // 98% JPEG quality
        'format' => 'png',         // Default to PNG for transparency
        'dpi' => 300,             // Print quality DPI
        'pixelRatio' => 4         // 4x multiplier for ultra high quality
    ];
    
    public function __construct($orderId) {
        $this->pdo = getDBConnection();
        $this->orderId = $orderId;
        $this->loadOrder();
        $this->loadDesignData();
    }
    
    private function loadOrder() {
        $stmt = $this->pdo->prepare("
            SELECT o.*, bi.image_path as existing_image_path
            FROM orders o
            LEFT JOIN billboard_images bi ON o.id = bi.order_id
            WHERE o.id = ? AND o.payment_status = 'completed'
        ");
        $stmt->execute([$this->orderId]);
        $this->order = $stmt->fetch();
        
        if (!$this->order) {
            throw new Exception("Order not found or payment not completed: {$this->orderId}");
        }
    }
    
    private function loadDesignData() {
        // Try to load design data from session first
        session_start();
        if (isset($_SESSION['payment_design_data']) && 
            $_SESSION['payment_design_data']['payment_intent_id'] === $this->order['payment_transaction_id']) {
            $this->designData = $_SESSION['payment_design_data']['design_data'];
            return;
        }
        
        // Fallback: try to load from order metadata or database
        $stmt = $this->pdo->prepare("
            SELECT design_data FROM billboard_images 
            WHERE order_id = ? AND design_data IS NOT NULL 
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$this->orderId]);
        $result = $stmt->fetch();
        
        if ($result && $result['design_data']) {
            $this->designData = json_decode($result['design_data'], true);
        } else {
            $this->designData = null;
        }
    }
    
    /**
     * Generate high-quality image based on captured design data
     */
    public function generateHighQualityImage() {
        try {
            // Check if high-quality image already exists
            if ($this->order['existing_image_path'] && 
                file_exists($this->order['existing_image_path']) && 
                $this->isHighQualityImage($this->order['existing_image_path'])) {
                
                return [
                    'success' => true,
                    'image_path' => $this->order['existing_image_path'],
                    'message' => 'High-quality image already exists',
                    'is_new' => false
                ];
            }
            
            $imagePath = null;
            
            if ($this->designData) {
                // Generate from captured design data
                $imagePath = $this->generateFromDesignData();
            } else {
                // Fallback to basic generation
                $imagePath = $this->generateFallbackImage();
            }
            
            if ($imagePath) {
                // Save image record to database
                $this->saveHighQualityImageRecord($imagePath);
                
                // Update order with image path
                $this->updateOrderWithImage($imagePath);
                
                return [
                    'success' => true,
                    'image_path' => $imagePath,
                    'message' => 'High-quality image generated successfully',
                    'is_new' => true,
                    'quality_settings' => $this->qualitySettings
                ];
            } else {
                throw new Exception('Failed to generate high-quality image');
            }
            
        } catch (Exception $e) {
            error_log("High-quality image generation failed for order {$this->orderId}: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate image from captured design data
     */
    private function generateFromDesignData() {
        if (!$this->designData) {
            throw new Exception('No design data available');
        }
        
        $billboardType = $this->designData['billboardType'] ?? $this->order['billboard_type'];
        
        if ($billboardType === 'custom') {
            return $this->generateCustomFromDesignData();
        } else {
            return $this->generateTemplatedFromDesignData();
        }
    }
    
    /**
     * Generate custom billboard from design data
     */
    private function generateCustomFromDesignData() {
        // If we have canvas image data, use it
        if (isset($this->designData['canvasImageData']) && $this->designData['canvasImageData']) {
            return $this->processCanvasImageData($this->designData['canvasImageData']);
        }
        
        // Otherwise, try to reconstruct from design elements
        return $this->reconstructFromDesignElements();
    }
    
    /**
     * Generate templated billboard from design data
     */
    private function generateTemplatedFromDesignData() {
        $templateId = $this->designData['designData']['templateId'] ?? null;
        $customizations = $this->designData['designData']['customizations'] ?? [];
        
        if (!$templateId) {
            throw new Exception('Template ID not found in design data');
        }
        
        // For now, create a high-quality placeholder with template info
        return $this->createTemplatedPlaceholder($templateId, $customizations);
    }
    
    /**
     * Process canvas image data to create high-quality version
     */
    private function processCanvasImageData($canvasImageData) {
        if (strpos($canvasImageData, 'data:image/') === 0) {
            // It's a base64 data URL
            return $this->saveBase64Image($canvasImageData);
        } else {
            // It might be HTML or other format - create placeholder
            return $this->createPlaceholderFromData($canvasImageData);
        }
    }
    
    /**
     * Save base64 image data as high-quality file
     */
    private function saveBase64Image($base64Data) {
        // Extract image format and data
        if (!preg_match('/^data:image\/(png|jpeg|jpg);base64,(.+)$/', $base64Data, $matches)) {
            throw new Exception('Invalid base64 image data format');
        }
        
        $imageFormat = $matches[1];
        $binaryData = base64_decode($matches[2]);
        
        if ($binaryData === false) {
            throw new Exception('Failed to decode base64 image data');
        }
        
        // Create customer directory
        $customerDir = $this->createCustomerDirectory();
        
        // Generate filename using the standard format
        $timestamp = date('Y-m-d_H-i-s');
        $randomId = substr(uniqid(), -8);
        $filename = "billboard_hq_{$this->order['order_number']}_{$timestamp}_{$randomId}.{$imageFormat}";
        $imagePath = $customerDir . '/' . $filename;
        
        // Save the image
        if (file_put_contents($imagePath, $binaryData) === false) {
            throw new Exception('Failed to save image file');
        }
        
        // Verify image quality
        if (!$this->verifyImageQuality($imagePath)) {
            unlink($imagePath);
            throw new Exception('Generated image does not meet quality standards');
        }
        
        return $imagePath;
    }
    
    /**
     * Create customer directory for image storage using the standard uploads structure
     */
    private function createCustomerDirectory() {
        // Use the same directory structure as the existing system
        $customerEmail = $this->order['customer_email'];
        $customerHash = substr(md5($customerEmail), 0, 8);

        // Use the uploads/billboards directory structure
        $baseDir = dirname(__DIR__, 2) . '/uploads/billboards';
        $customerDir = $baseDir . '/customer-' . $customerHash;

        if (!is_dir($customerDir)) {
            if (!mkdir($customerDir, 0755, true)) {
                throw new Exception('Failed to create customer directory');
            }
        }

        return $customerDir;
    }
    
    /**
     * Verify image meets quality standards
     */
    private function verifyImageQuality($imagePath) {
        if (!file_exists($imagePath)) {
            return false;
        }
        
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }
        
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $fileSize = filesize($imagePath);
        
        // Check minimum dimensions (should be high quality)
        if ($width < 1600 || $height < 800) {
            error_log("Image quality check failed: dimensions too small ({$width}x{$height})");
            return false;
        }
        
        // Check file size (should be substantial for high quality)
        if ($fileSize < 100000) { // Less than 100KB is suspicious for high quality
            error_log("Image quality check failed: file size too small ({$fileSize} bytes)");
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if existing image is high quality
     */
    private function isHighQualityImage($imagePath) {
        return $this->verifyImageQuality($imagePath);
    }
    
    /**
     * Generate fallback image when no design data is available
     */
    private function generateFallbackImage() {
        return $this->createHighQualityPlaceholder();
    }
    
    /**
     * Create high-quality placeholder image
     */
    private function createHighQualityPlaceholder() {
        $customerDir = $this->createCustomerDirectory();
        $timestamp = date('Y-m-d_H-i-s');
        $randomId = substr(uniqid(), -8);
        $filename = "billboard_placeholder_hq_{$this->order['order_number']}_{$timestamp}_{$randomId}.png";
        $imagePath = $customerDir . '/' . $filename;
        
        // Create high-quality placeholder using GD
        $image = imagecreatetruecolor($this->qualitySettings['width'], $this->qualitySettings['height']);
        
        // Set background color
        $backgroundColor = imagecolorallocate($image, 240, 240, 240);
        imagefill($image, 0, 0, $backgroundColor);
        
        // Add text
        $textColor = imagecolorallocate($image, 60, 60, 60);
        $text = "High-Quality Billboard\nOrder: " . $this->order['order_number'];
        
        // Use a built-in font (font 5 is the largest built-in font)
        $fontSize = 5;
        $textWidth = imagefontwidth($fontSize) * strlen("High-Quality Billboard");
        $textHeight = imagefontheight($fontSize) * 2; // 2 lines
        
        $x = ($this->qualitySettings['width'] - $textWidth) / 2;
        $y = ($this->qualitySettings['height'] - $textHeight) / 2;
        
        imagestring($image, $fontSize, $x, $y, "High-Quality Billboard", $textColor);
        imagestring($image, $fontSize, $x - 50, $y + 30, "Order: " . $this->order['order_number'], $textColor);
        
        // Save as PNG
        if (!imagepng($image, $imagePath, 0)) {
            imagedestroy($image);
            throw new Exception('Failed to save placeholder image');
        }
        
        imagedestroy($image);
        return $imagePath;
    }

    /**
     * Create templated placeholder with customizations
     */
    private function createTemplatedPlaceholder($templateId, $customizations) {
        $customerDir = $this->createCustomerDirectory();
        $timestamp = date('Y-m-d_H-i-s');
        $randomId = substr(uniqid(), -8);
        $filename = "billboard_templated_hq_{$this->order['order_number']}_{$timestamp}_{$randomId}.png";
        $imagePath = $customerDir . '/' . $filename;

        // Create high-quality templated image
        $image = imagecreatetruecolor($this->qualitySettings['width'], $this->qualitySettings['height']);

        // Set background color (template-like)
        $backgroundColor = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $backgroundColor);

        // Add border
        $borderColor = imagecolorallocate($image, 200, 200, 200);
        imagerectangle($image, 10, 10, $this->qualitySettings['width'] - 10, $this->qualitySettings['height'] - 10, $borderColor);

        // Add template info
        $textColor = imagecolorallocate($image, 60, 60, 60);
        $fontSize = 5;

        imagestring($image, $fontSize, 50, 50, "Templated Billboard - Template ID: " . $templateId, $textColor);
        imagestring($image, $fontSize, 50, 80, "Order: " . $this->order['order_number'], $textColor);
        imagestring($image, $fontSize, 50, 110, "Customer: " . $this->order['customer_name'], $textColor);

        // Add customization info
        $y = 150;
        foreach ($customizations as $key => $value) {
            if ($y > $this->qualitySettings['height'] - 50) break;
            $text = $key . ": " . (is_array($value) ? json_encode($value) : $value);
            $text = substr($text, 0, 80); // Limit text length
            imagestring($image, 3, 50, $y, $text, $textColor);
            $y += 25;
        }

        // Save as PNG
        if (!imagepng($image, $imagePath, 0)) {
            imagedestroy($image);
            throw new Exception('Failed to save templated image');
        }

        imagedestroy($image);
        return $imagePath;
    }

    /**
     * Reconstruct image from design elements
     */
    private function reconstructFromDesignElements() {
        // This would be a complex implementation to recreate the canvas
        // For now, create a placeholder that indicates reconstruction was attempted
        return $this->createReconstructionPlaceholder();
    }

    /**
     * Create placeholder from HTML or other data
     */
    private function createPlaceholderFromData($data) {
        $customerDir = $this->createCustomerDirectory();
        $timestamp = date('Y-m-d_H-i-s');
        $randomId = substr(uniqid(), -8);
        $filename = "billboard_data_hq_{$this->order['order_number']}_{$timestamp}_{$randomId}.png";
        $imagePath = $customerDir . '/' . $filename;

        // Create image with data info
        $image = imagecreatetruecolor($this->qualitySettings['width'], $this->qualitySettings['height']);
        $backgroundColor = imagecolorallocate($image, 250, 250, 250);
        imagefill($image, 0, 0, $backgroundColor);

        $textColor = imagecolorallocate($image, 60, 60, 60);
        imagestring($image, 5, 50, 50, "Billboard from Captured Data", $textColor);
        imagestring($image, 3, 50, 80, "Order: " . $this->order['order_number'], $textColor);
        imagestring($image, 3, 50, 100, "Data Type: " . (is_string($data) ? 'HTML/Text' : 'Object'), $textColor);

        imagepng($image, $imagePath, 0);
        imagedestroy($image);

        return $imagePath;
    }

    /**
     * Create reconstruction placeholder
     */
    private function createReconstructionPlaceholder() {
        $customerDir = $this->createCustomerDirectory();
        $timestamp = date('Y-m-d_H-i-s');
        $randomId = substr(uniqid(), -8);
        $filename = "billboard_reconstructed_hq_{$this->order['order_number']}_{$timestamp}_{$randomId}.png";
        $imagePath = $customerDir . '/' . $filename;

        $image = imagecreatetruecolor($this->qualitySettings['width'], $this->qualitySettings['height']);
        $backgroundColor = imagecolorallocate($image, 245, 245, 255);
        imagefill($image, 0, 0, $backgroundColor);

        $textColor = imagecolorallocate($image, 60, 60, 60);
        imagestring($image, 5, 50, 50, "Reconstructed Billboard", $textColor);
        imagestring($image, 3, 50, 80, "Order: " . $this->order['order_number'], $textColor);
        imagestring($image, 3, 50, 100, "Generated from design elements", $textColor);

        imagepng($image, $imagePath, 0);
        imagedestroy($image);

        return $imagePath;
    }

    /**
     * Save high-quality image record to database
     */
    private function saveHighQualityImageRecord($imagePath) {
        $fileInfo = $this->validateImageFile($imagePath);

        if (!$fileInfo['valid']) {
            throw new Exception('Generated image is invalid: ' . $fileInfo['error']);
        }

        $stmt = $this->pdo->prepare("
            INSERT INTO billboard_images (
                order_id, image_filename, image_path, image_size_bytes,
                image_width, image_height, image_format, design_data,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $designDataJson = $this->designData ? json_encode($this->designData) : null;

        $stmt->execute([
            $this->orderId,
            basename($imagePath),
            $imagePath,
            $fileInfo['size'],
            $fileInfo['width'],
            $fileInfo['height'],
            $fileInfo['format'],
            $designDataJson
        ]);

        return $this->pdo->lastInsertId();
    }

    /**
     * Update order with generated image path
     */
    private function updateOrderWithImage($imagePath) {
        $stmt = $this->pdo->prepare("
            UPDATE orders
            SET billboard_image_path = ?, image_generated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$imagePath, $this->orderId]);
    }

    /**
     * Validate image file
     */
    private function validateImageFile($imagePath) {
        if (!file_exists($imagePath)) {
            return ['valid' => false, 'error' => 'Image file does not exist'];
        }

        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return ['valid' => false, 'error' => 'Invalid image file'];
        }

        $fileSize = filesize($imagePath);
        $mimeType = $imageInfo['mime'];
        $format = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));

        return [
            'valid' => true,
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'size' => $fileSize,
            'format' => $format,
            'mime_type' => $mimeType
        ];
    }
}
