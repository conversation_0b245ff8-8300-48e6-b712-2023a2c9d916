// ========================================
// EXPORT MANAGEMENT SYSTEM
// ========================================

class ExportManager {
    constructor() {
        this.isExporting = false;
        this.exportQuality = 'standard';
        this.exportFormat = 'png';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Export button
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportBillboard();
            });
        }

        // Proceed to payment button
        const proceedPaymentBtn = document.getElementById('proceedPaymentBtn');
        if (proceedPaymentBtn) {
            proceedPaymentBtn.addEventListener('click', async () => {
                await this.proceedToPayment();
            });
        }
    }

    // Export billboard as image
    async exportBillboard() {
        if (this.isExporting) {
            return;
        }

        const canvas = window.canvasManager.getCanvas();
        if (!canvas) {
            alert('No billboard to export. Please select a template first.');
            return;
        }

        try {
            this.isExporting = true;

            // Use the high-quality image generator
            if (window.highQualityImageGenerator) {
                const result = await window.highQualityImageGenerator.generateAndSaveImage(canvas);

                if (result.success) {
                    // Also download the image
                    this.downloadImage(result.image.url, result.image.filename);
                    return result;
                } else {
                    throw new Error(result.error || 'Failed to generate image');
                }
            }

            // Fallback to original export method
            this.showExportProgress('Preparing export...');

            // Get export configuration
            const quality = this.getExportQuality();
            const format = this.getExportFormat();

            // Prepare canvas for export
            await this.prepareCanvasForExport(canvas);

            // Configure export options based on quality
            const options = this.getExportOptions(quality, format);

            // Debug logging for export quality verification
            console.log('Export Configuration:', {
                quality: quality,
                format: format,
                pixelRatio: options.pixelRatio,
                dimensions: `${options.width}x${options.height}`,
                canvasDimensions: `${options.canvasWidth}x${options.canvasHeight}`,
                currentCanvasSize: `${canvas.offsetWidth}x${canvas.offsetHeight}`,
                jpegQuality: options.quality
            });

            this.showExportProgress('Capturing high-quality image...');

            // Export the image
            let dataUrl;
            if (format === 'jpeg') {
                dataUrl = await htmlToImage.toJpeg(canvas, options);
            } else {
                dataUrl = await htmlToImage.toPng(canvas, options);
            }

            // Download the image
            this.downloadImage(dataUrl, format);

            this.showExportProgress('Export completed!');
            setTimeout(() => this.hideExportProgress(), 2000);

        } catch (error) {
            console.error('Export failed:', error);
            alert('Export failed. Please try again.');
            this.hideExportProgress();
        } finally {
            this.isExporting = false;
            this.restoreCanvasAfterExport(canvas);
        }
    }

    // Get export quality setting
    getExportQuality() {
        // Always use HIGH quality for best results on both desktop and mobile
        // This ensures consistent quality regardless of device
        return 'high'; // Ultra high quality (4x pixel ratio, 98% JPEG quality)
    }

    // Get export format setting
    getExportFormat() {
        // For now, return PNG
        // This could be extended to read from UI controls
        return 'png';
    }

    // Get export options based on quality and format - HIGH QUALITY SYSTEM
    getExportOptions(quality, format) {
        // Use FIXED base dimensions for consistent high quality (like custom billboard)
        const baseWidth = 800;  // Original template design width
        const baseHeight = 400; // Original template design height

        // Get device pixel ratio but ensure minimum quality
        const devicePixelRatio = window.devicePixelRatio || 1;

        // Configure quality settings with consistent high-quality output
        let pixelRatio, canvasMultiplier, jpegQuality;

        switch (quality) {
            case 'high':
                pixelRatio = Math.max(4, devicePixelRatio); // Ultra high quality
                canvasMultiplier = 4;
                jpegQuality = 0.98;
                break;
            case 'web':
                pixelRatio = Math.max(2, devicePixelRatio); // Good quality for web
                canvasMultiplier = 2;
                jpegQuality = 0.85;
                break;
            default: // standard
                pixelRatio = Math.max(3, devicePixelRatio); // High quality for print
                canvasMultiplier = 3;
                jpegQuality = 0.95;
        }

        const baseOptions = {
            quality: jpegQuality,
            pixelRatio: pixelRatio,
            backgroundColor: format === 'png' ? 'transparent' : '#ffffff',
            cacheBust: true,

            // Use FIXED base dimensions regardless of current canvas size (KEY FOR QUALITY)
            width: baseWidth,
            height: baseHeight,
            canvasWidth: baseWidth * canvasMultiplier,
            canvasHeight: baseHeight * canvasMultiplier,

            // Force original dimensions and disable responsive scaling during export
            style: {
                position: 'relative',
                overflow: 'visible',
                transform: 'none',
                transformOrigin: 'top left',
                width: baseWidth + 'px',
                height: baseHeight + 'px',
                maxWidth: 'none',
                minWidth: 'none',
                minHeight: 'none',
                contain: 'none',
                boxSizing: 'border-box',
                margin: '0',
                padding: '0'
            },

            // Filter out UI elements
            filter: (node) => {
                if (node.classList) {
                    return !node.classList.contains('text-customize-btn') &&
                           !node.classList.contains('selected') &&
                           !node.hasAttribute('data-ui-element');
                }
                return true;
            },

            // Handle CORS and fonts gracefully (avoid Google Fonts CORS issues)
            fontEmbedCSS: false,  // Disable font embedding to avoid CORS errors
            includeQueryParams: false,
            skipAutoScale: false,
            skipFonts: true,      // Skip font processing to avoid CORS
            useCORS: false,
            allowTaint: true      // Allow tainted canvas for better compatibility
        };

        return baseOptions;
    }

    // Get pixel ratio based on quality
    getPixelRatio(quality) {
        switch (quality) {
            case 'web':
                return 2;  // Good quality for web
            case 'standard':
                return 3;  // High quality for print
            case 'high':
                return 4;  // Ultra high quality
            default:
                return 3;
        }
    }

    // Prepare canvas for export - HIGH QUALITY SYSTEM
    async prepareCanvasForExport(canvas) {
        // Store original canvas state to prevent layout shifts
        this.originalCanvasStyle = {
            width: canvas.style.width,
            height: canvas.style.height,
            maxWidth: canvas.style.maxWidth,
            minWidth: canvas.style.minWidth,
            minHeight: canvas.style.minHeight,
            transform: canvas.style.transform,
            contain: canvas.style.contain,
            overflow: canvas.style.overflow,
            position: canvas.style.position,
            aspectRatio: canvas.style.aspectRatio
        };

        // Set export flag for CSS targeting
        canvas.setAttribute('data-exporting', 'true');

        // Force canvas to ORIGINAL dimensions to prevent mobile scaling issues
        const baseWidth = 800;  // Original template design width
        const baseHeight = 400; // Original template design height

        // Temporarily override responsive scaling with fixed dimensions
        canvas.style.width = baseWidth + 'px';
        canvas.style.height = baseHeight + 'px';
        canvas.style.maxWidth = 'none';
        canvas.style.minWidth = 'none';
        canvas.style.minHeight = 'none';
        canvas.style.transform = 'none';
        canvas.style.contain = 'none';
        canvas.style.overflow = 'visible';
        canvas.style.position = 'relative';
        canvas.style.aspectRatio = 'unset'; // Disable aspect-ratio during export

        // Hide any UI elements that shouldn't be in export
        const uiElements = canvas.querySelectorAll('.text-customize-btn, .selected');
        this.hiddenElements = [];

        uiElements.forEach(el => {
            this.hiddenElements.push({
                element: el,
                originalDisplay: el.style.display
            });
            el.style.display = 'none';
        });

        // Ensure all images are loaded
        const images = canvas.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve;
                }
            });
        });

        await Promise.all(imagePromises);

        // Small delay to ensure rendering is complete with new dimensions
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Restore canvas after export - HIGH QUALITY SYSTEM
    restoreCanvasAfterExport(canvas) {
        // Remove export flag
        canvas.removeAttribute('data-exporting');

        // Restore original canvas styles to prevent layout shifts
        if (this.originalCanvasStyle) {
            canvas.style.width = this.originalCanvasStyle.width;
            canvas.style.height = this.originalCanvasStyle.height;
            canvas.style.maxWidth = this.originalCanvasStyle.maxWidth;
            canvas.style.minWidth = this.originalCanvasStyle.minWidth;
            canvas.style.minHeight = this.originalCanvasStyle.minHeight;
            canvas.style.transform = this.originalCanvasStyle.transform;
            canvas.style.contain = this.originalCanvasStyle.contain;
            canvas.style.overflow = this.originalCanvasStyle.overflow;
            canvas.style.position = this.originalCanvasStyle.position;
            canvas.style.aspectRatio = this.originalCanvasStyle.aspectRatio;
        }

        // Restore hidden UI elements
        if (this.hiddenElements) {
            this.hiddenElements.forEach(item => {
                item.element.style.display = item.originalDisplay;
            });
            this.hiddenElements = null;
        }

        // Clear stored styles
        this.originalCanvasStyle = null;
    }

    // Download image
    downloadImage(dataUrl, format) {
        const link = document.createElement('a');
        link.download = `billboard-${Date.now()}.${format}`;
        link.href = dataUrl;
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Show export progress
    showExportProgress(message) {
        // Create or update progress indicator
        let progressEl = document.getElementById('exportProgress');
        if (!progressEl) {
            progressEl = document.createElement('div');
            progressEl.id = 'exportProgress';
            progressEl.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px 30px;
                border-radius: 10px;
                z-index: 10000;
                font-size: 16px;
                text-align: center;
            `;
            document.body.appendChild(progressEl);
        }
        
        progressEl.textContent = message;
        progressEl.style.display = 'block';
    }

    // Hide export progress
    hideExportProgress() {
        const progressEl = document.getElementById('exportProgress');
        if (progressEl) {
            progressEl.style.display = 'none';
        }
    }

    // Proceed to payment using unified checkout
    async proceedToPayment() {
        // Use the unified checkout system
        if (window.openUnifiedCheckout) {
            try {
                await window.openUnifiedCheckout();
            } catch (error) {
                console.error('Error opening checkout:', error);
                alert('Error opening checkout. Please try again.');
            }
        } else {
            alert('Checkout system not available. Please refresh the page and try again.');
        }
    }

    // Check if export is available
    isExportAvailable() {
        const canvas = window.canvasManager.getCanvas();
        const template = window.canvasManager.getCurrentTemplate();
        return canvas && template;
    }

    // Get export status
    getExportStatus() {
        return {
            isExporting: this.isExporting,
            isAvailable: this.isExportAvailable()
        };
    }

    // Download image from URL
    downloadImage(imageUrl, filename) {
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = filename || 'billboard-template.png';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Global functions for HTML onclick handlers
function exportBillboard() {
    window.exportManager.exportBillboard();
}

async function proceedToPayment() {
    await window.exportManager.proceedToPayment();
}

// Create global instance
window.exportManager = new ExportManager();
