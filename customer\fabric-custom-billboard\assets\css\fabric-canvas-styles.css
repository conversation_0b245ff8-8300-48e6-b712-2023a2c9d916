/* ========================================
   FABRIC.JS CANVAS SPECIFIC STYLES
   ======================================== */

/* Canvas Section - Clean Container */
.canvas-section {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: transparent;
    padding: 10px;
    position: relative;
}

/* Canvas Wrapper - Ensure Single Canvas Display and Proper Scaling */
.canvas-wrapper {
    position: relative;
    display: inline-block;
    transform-origin: center center;
    transition: transform 0.3s ease;
}

/* Ensure canvas elements stack properly */
.canvas-wrapper canvas {
    display: block;
    position: relative;
}

/* Fabric.js Canvas Element with Thick Black Border */
#fabricCanvas {
    border: 4px solid #000000 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Fabric.js Canvas Elements - Fix Two Boxes Issue */
.canvas-section .upper-canvas {
    border: none !important;
    border-radius: 0 !important;
    outline: none !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
}

.canvas-section .lower-canvas {
    border: 4px solid #000000 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Fabric.js Object Controls */
.fabric-object-controls {
    /* Custom styling for Fabric.js selection controls */
}

/* Selection Handles - Mobile Optimized */
.canvas-section .upper-canvas {
    touch-action: none; /* Prevent default touch behaviors */
}

/* Custom Control Styles for Mobile */
.fabric-control-corner {
    width: 16px !important;
    height: 16px !important;
    border: 2px solid var(--primary-color) !important;
    background: var(--white) !important;
    border-radius: 50% !important;
    box-shadow: var(--shadow-sm) !important;
}

/* Touch-friendly control sizing */
@media (hover: none) and (pointer: coarse) {
    .fabric-control-corner {
        width: 20px !important;
        height: 20px !important;
        border-width: 3px !important;
    }
}

/* Fabric.js Text Editing Styles */
.fabric-text-editing {
    outline: none !important;
    border: 1px dashed var(--primary-color) !important;
    background: rgba(37, 99, 235, 0.05) !important;
}

/* Custom Fabric.js Object Styles */
.fabric-object {
    transition: all 0.2s ease;
}

.fabric-object:hover {
    filter: brightness(1.05);
}

.fabric-object.selected {
    filter: brightness(1.1);
}

/* Fabric.js Group Selection */
.fabric-group-selection {
    border: 2px dashed var(--primary-color) !important;
    background: rgba(37, 99, 235, 0.1) !important;
}

/* Canvas Background Patterns */
.canvas-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(45deg, var(--gray-100) 25%, transparent 25%),
        linear-gradient(-45deg, var(--gray-100) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, var(--gray-100) 75%),
        linear-gradient(-45deg, transparent 75%, var(--gray-100) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    opacity: 0.3;
    pointer-events: none;
    z-index: -1;
}

/* Hide background pattern when canvas has background */
.canvas-wrapper.has-background::before {
    display: none;
}

/* Fabric.js Performance Optimizations */
.fabric-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
}

/* High DPI Canvas Rendering */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .fabric-canvas {
        image-rendering: auto;
    }
}

/* Fabric.js Object Animation Classes */
.fabric-object-animate-in {
    animation: fadeInScale 0.3s ease-out;
}

.fabric-object-animate-out {
    animation: fadeOutScale 0.2s ease-in;
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeOutScale {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* Custom Fabric.js Cursor Styles */
.fabric-canvas.crosshair {
    cursor: crosshair !important;
}

.fabric-canvas.grab {
    cursor: grab !important;
}

.fabric-canvas.grabbing {
    cursor: grabbing !important;
}

.fabric-canvas.text {
    cursor: text !important;
}

.fabric-canvas.pointer {
    cursor: pointer !important;
}

/* Fabric.js Loading States */
.fabric-canvas.loading {
    opacity: 0.7;
    pointer-events: none;
}

.fabric-canvas.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: var(--z-dropdown);
}

/* Fabric.js Object Layering Indicators */
.fabric-object-layer-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    z-index: var(--z-dropdown);
}

/* Fabric.js Snap Guidelines */
.fabric-snap-line {
    stroke: var(--primary-color) !important;
    stroke-width: 1 !important;
    stroke-dasharray: 5,5 !important;
    opacity: 0.8 !important;
}

/* Fabric.js Object Alignment Helpers */
.fabric-alignment-helper {
    stroke: var(--success-color) !important;
    stroke-width: 2 !important;
    opacity: 0.6 !important;
}

/* Custom Fabric.js Text Object Styles */
.fabric-text-object {
    font-family: inherit;
    line-height: 1.2;
}

/* Fabric.js Image Object Styles */
.fabric-image-object {
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
}

/* Fabric.js Group Object Styles */
.fabric-group-object {
    border: 1px dashed var(--gray-400);
    background: rgba(255, 255, 255, 0.1);
}

/* Responsive Canvas Scaling */
.canvas-wrapper.mobile-scale {
    transform-origin: center center;
}

.canvas-wrapper.mobile-scale .fabric-canvas {
    transform-origin: center center;
}

/* Touch Gesture Indicators */
.touch-gesture-indicator {
    position: absolute;
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    background: rgba(37, 99, 235, 0.1);
    pointer-events: none;
    z-index: var(--z-popover);
    animation: touchPulse 0.3s ease-out;
}

@keyframes touchPulse {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Fabric.js Export Preparation */
.fabric-canvas.export-mode {
    background: var(--white) !important;
}

.fabric-canvas.export-mode .fabric-object {
    filter: none !important;
}

/* Fabric.js Object Context Menu */
.fabric-context-menu {
    position: absolute;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--space-2);
    z-index: var(--z-popover);
    min-width: 150px;
}

.fabric-context-menu-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: var(--text-sm);
    color: var(--gray-700);
    transition: all 0.2s ease;
}

.fabric-context-menu-item:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.fabric-context-menu-item i {
    width: 16px;
    text-align: center;
}

.fabric-context-menu-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--space-2) 0;
}

/* Fabric.js Object Transformation Guides */
.fabric-transform-guide {
    stroke: var(--warning-color) !important;
    stroke-width: 1 !important;
    stroke-dasharray: 3,3 !important;
    opacity: 0.7 !important;
}

/* Fabric.js Multi-Selection Styles */
.fabric-multi-selection {
    border: 2px solid var(--primary-color) !important;
    background: rgba(37, 99, 235, 0.05) !important;
}

/* Fabric.js Object Locking Indicators */
.fabric-object-locked::after {
    content: '🔒';
    position: absolute;
    top: -12px;
    left: -12px;
    font-size: 12px;
    background: var(--warning-color);
    color: var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-dropdown);
}

/* Fabric.js Canvas Zoom Indicators */
.canvas-zoom-indicator {
    position: absolute;
    bottom: var(--space-4);
    left: var(--space-4);
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: 500;
    z-index: var(--z-sticky);
    backdrop-filter: blur(4px);
}

/* Accessibility Enhancements */
.fabric-canvas:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .fabric-object,
    .fabric-object-animate-in,
    .fabric-object-animate-out,
    .touch-gesture-indicator {
        animation: none !important;
        transition: none !important;
    }
    
    .fabric-canvas.loading::after {
        animation: none !important;
        border-top-color: var(--primary-color);
    }
}

/* Print Styles */
@media print {
    .canvas-wrapper::before,
    .fabric-control-corner,
    .fabric-context-menu,
    .canvas-zoom-indicator,
    .touch-gesture-indicator {
        display: none !important;
    }

    .fabric-canvas {
        background: var(--white) !important;
    }
}
