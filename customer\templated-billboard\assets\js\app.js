// ========================================
// MAIN APPLICATION CONTROLLER
// ========================================

class TemplatedBillboardApp {
    constructor() {
        this.currentCategory = null;
        this.selectedTemplateId = null;
        
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupApplication();
            });
        } else {
            this.setupApplication();
        }
    }

    setupApplication() {
        console.log('Initializing Templated Billboard App...');

        try {
            // Setup event listeners
            this.setupEventListeners();

            // Initialize UI state
            this.initializeUI();

            console.log('App initialized successfully');
        } catch (error) {
            console.error('Error initializing app:', error);
            this.showError('Failed to initialize application. Please refresh the page.');
        }
    }

    setupEventListeners() {
        // Category dropdown change
        const categorySelect = document.getElementById('categorySelect');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                this.onCategoryChange(e.target.value);
            });
        }

        // Control buttons
        const changeBackgroundBtn = document.getElementById('changeBackgroundBtn');
        if (changeBackgroundBtn) {
            changeBackgroundBtn.addEventListener('click', () => {
                window.customizationManager.showBackgroundModal();
            });
        }

        const uploadImageBtn = document.getElementById('uploadImageBtn');
        if (uploadImageBtn) {
            uploadImageBtn.addEventListener('click', () => {
                window.customizationManager.handleImageUpload();
            });
        }

        // File upload handler
        const imageUpload = document.getElementById('imageUpload');
        if (imageUpload) {
            imageUpload.addEventListener('change', (e) => {
                this.handleImageUpload(e);
            });
        }
    }

    initializeUI() {
        // Ensure all managers are initialized
        if (!window.templateManager) {
            console.error('Template Manager not initialized');
            return;
        }

        if (!window.canvasManager) {
            console.error('Canvas Manager not initialized');
            return;
        }

        if (!window.customizationManager) {
            console.error('Customization Manager not initialized');
            return;
        }

        if (!window.exportManager) {
            console.error('Export Manager not initialized');
            return;
        }

        // Set initial state
        this.updateTemplatesGrid();
    }

    // Handle category selection change
    onCategoryChange(category) {
        console.log('Category changed to:', category);

        this.currentCategory = category;
        this.selectedTemplateId = null;

        // Update template manager
        window.templateManager.setCurrentCategory(category);

        // Update templates grid
        this.updateTemplatesGrid();

        // Hide text fields panel
        this.hideTextFieldsPanel();

        // Clear canvas if no category selected
        if (!category) {
            window.canvasManager.clearCanvas();
        }
    }

    // Update templates grid based on selected category
    updateTemplatesGrid() {
        const templatesGrid = document.getElementById('templatesGrid');
        if (!templatesGrid) return;

        // Clear existing content
        templatesGrid.innerHTML = '';

        if (!this.currentCategory) {
            templatesGrid.innerHTML = '<div class="loading">Select a category to view templates</div>';
            return;
        }

        // Get templates for current category
        const templates = window.templateManager.getTemplatesForCategory(this.currentCategory);
        const templateIds = Object.keys(templates);

        if (templateIds.length === 0) {
            templatesGrid.innerHTML = '<div class="loading">No templates available for this category</div>';
            return;
        }

        // Create template cards
        templateIds.forEach((templateId, index) => {
            const template = templates[templateId];
            const card = this.createTemplateCard(templateId, template, index + 1);
            templatesGrid.appendChild(card);
        });
    }

    // Create a template card
    createTemplateCard(templateId, template, displayNumber) {
        const card = document.createElement('div');
        card.className = 'template-card';
        card.dataset.templateId = templateId;

        // Create preview
        const preview = document.createElement('div');
        preview.className = 'template-preview';
        
        // Set preview background
        const previewUrl = window.templateManager.getTemplatePreviewUrl(template);
        if (previewUrl) {
            preview.style.backgroundImage = `url('${previewUrl}')`;
        } else if (template.defaultBackground) {
            preview.style.backgroundColor = template.defaultBackground;
        } else {
            preview.style.backgroundColor = '#f1f3f4';
        }

        // Create name
        const name = document.createElement('div');
        name.className = 'template-name';
        name.textContent = `Template ${displayNumber}`;

        // Add click handler
        card.addEventListener('click', () => {
            this.selectTemplate(templateId, template);
        });

        card.appendChild(preview);
        card.appendChild(name);

        return card;
    }

    // Select a template
    selectTemplate(templateId, template) {
        console.log('Template selected:', templateId, template);

        // Update selection state
        this.selectedTemplateId = templateId;
        window.templateManager.setSelectedTemplate(templateId);

        // Update UI
        this.updateTemplateSelection();

        // Load template onto canvas
        window.canvasManager.loadTemplate(template, templateId, this.currentCategory);

        // Show text fields panel
        this.showTextFieldsPanel(template);
    }

    // Show text fields panel with inline editing
    showTextFieldsPanel(template) {
        const panel = document.getElementById('textFieldsPanel');
        const container = document.getElementById('textFieldsContainer');

        if (!panel || !container) return;

        // Clear existing fields
        container.innerHTML = '';

        // Create text field inputs
        if (template.defaultTexts) {
            template.defaultTexts.forEach((text, index) => {
                const fieldGroup = this.createTextFieldGroup(text, index);
                container.appendChild(fieldGroup);
            });
        }

        // Show panel
        panel.classList.add('visible');
    }

    // Hide text fields panel
    hideTextFieldsPanel() {
        const panel = document.getElementById('textFieldsPanel');
        if (panel) {
            panel.classList.remove('visible');
        }
    }

    // Create text field group
    createTextFieldGroup(text, index) {
        const group = document.createElement('div');
        group.className = 'text-field-group';

        const label = document.createElement('label');
        label.className = 'text-field-label';
        label.textContent = `Text ${index + 1}:`;

        const input = document.createElement('input');
        input.className = 'text-field-input';
        input.type = 'text';
        input.value = text;
        input.dataset.index = index;

        // Add input event listener for real-time updates
        input.addEventListener('input', (e) => {
            this.updateTextContent(index, e.target.value);
        });

        const customizeBtn = document.createElement('button');
        customizeBtn.className = 'text-customize-btn';
        customizeBtn.textContent = 'Customize Style';
        customizeBtn.addEventListener('click', () => {
            this.showTextCustomizationModal(index);
        });

        group.appendChild(label);
        group.appendChild(input);
        group.appendChild(customizeBtn);

        return group;
    }

    // Update text content in real-time
    updateTextContent(index, newText) {
        const textElements = window.canvasManager.getTextElements();
        if (textElements[index]) {
            window.canvasManager.updateTextContent(textElements[index], newText);
            // Ensure single-line behavior is maintained
            this.enforceSingleLineText(textElements[index]);
        }
    }

    // Enforce single-line text behavior
    enforceSingleLineText(element) {
        if (element) {
            element.style.whiteSpace = 'nowrap';
            element.style.overflow = 'visible';
            element.style.textOverflow = 'clip';
            element.style.wordWrap = 'normal';
            element.style.wordBreak = 'normal';
            element.style.hyphens = 'none';
        }
    }

    // Show text customization modal for specific text element
    showTextCustomizationModal(index) {
        const textElements = window.canvasManager.getTextElements();
        if (textElements[index]) {
            // Select the text element and show customization
            window.canvasManager.selectElement(textElements[index]);
        }
    }

    // Update template selection UI
    updateTemplateSelection() {
        // Remove previous selection
        const previousSelected = document.querySelector('.template-card.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }

        // Add selection to current template
        if (this.selectedTemplateId) {
            const currentCard = document.querySelector(`[data-template-id="${this.selectedTemplateId}"]`);
            if (currentCard) {
                currentCard.classList.add('selected');
            }
        }
    }

    // Handle image upload
    handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file.');
            return;
        }

        if (file.size > 5 * 1024 * 1024) {
            alert('Image file is too large. Please select an image under 5MB.');
            return;
        }

        // Read file and update canvas
        const reader = new FileReader();
        reader.onload = (e) => {
            window.canvasManager.updateImageSource(e.target.result);
        };
        reader.readAsDataURL(file);

        // Clear the input so the same file can be selected again
        event.target.value = '';
    }

    // Get current state
    getCurrentState() {
        return {
            category: this.currentCategory,
            templateId: this.selectedTemplateId,
            hasTemplate: !!this.selectedTemplateId
        };
    }

    // Reset application
    reset() {
        this.currentCategory = null;
        this.selectedTemplateId = null;

        // Reset UI
        const categorySelect = document.getElementById('categorySelect');
        if (categorySelect) {
            categorySelect.value = '';
        }

        // Clear canvas
        window.canvasManager.clearCanvas();

        // Update templates grid
        this.updateTemplatesGrid();
    }

    // Show error message
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;

        const container = document.querySelector('.container');
        if (container) {
            container.insertBefore(errorDiv, container.firstChild);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }
    }
}

// Initialize app when script loads
window.templatedBillboardApp = new TemplatedBillboardApp();

// Global utility functions
function goBack() {
    // Navigate back to parent directory or main page
    if (window.history.length > 1) {
        window.history.back();
    } else {
        // Fallback - redirect to parent directory
        window.location.href = '../';
    }
}

// Debug function for development
function debugApp() {
    console.log('=== Templated Billboard App Debug Info ===');
    console.log('App State:', window.templatedBillboardApp.getCurrentState());
    console.log('Template Manager:', window.templateManager);
    console.log('Canvas Manager:', window.canvasManager);
    console.log('Customization Manager:', window.customizationManager);
    console.log('Export Manager:', window.exportManager);
    console.log('==========================================');
}

// Make debug function available globally
window.debugApp = debugApp;
