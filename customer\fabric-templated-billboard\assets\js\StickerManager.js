/**
 * StickerManager - Handles sticker selection and canvas integration
 * Mobile-first responsive design with touch-friendly interactions
 */

class StickerManager {
    constructor(canvasManager) {
        this.canvasManager = canvasManager;
        this.selectedSticker = null;
        this.currentCategory = 'icons';
        this.recentStickers = this.loadRecentStickers();
        this.searchQuery = '';
        
        this.init();
    }

    init() {
        this.createStickerInterface();
        this.setupEventListeners();
        this.loadCategory(this.currentCategory);
    }

    createStickerInterface() {
        const container = document.getElementById('stickerContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="sticker-interface">
                <!-- Search Bar -->
                <div class="sticker-search">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="stickerSearch" placeholder="Search stickers..." 
                               class="search-input" autocomplete="off">
                        <button class="search-clear" id="clearStickerSearch" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Category Tabs -->
                <div class="sticker-categories">
                    <div class="category-tabs" id="stickerCategoryTabs">
                        <!-- Categories will be populated here -->
                    </div>
                </div>

                <!-- Recent Stickers (if any) -->
                <div class="recent-stickers" id="recentStickersSection" style="display: none;">
                    <h4 class="section-title">
                        <i class="fas fa-clock"></i>
                        <span>Recently Used</span>
                    </h4>
                    <div class="sticker-grid recent-grid" id="recentStickersGrid">
                        <!-- Recent stickers will be populated here -->
                    </div>
                </div>

                <!-- Sticker Grid -->
                <div class="sticker-content">
                    <div class="sticker-grid" id="stickerGrid">
                        <!-- Stickers will be populated here -->
                    </div>
                </div>

                <!-- Selected Sticker Preview -->
                <div class="sticker-preview" id="stickerPreview" style="display: none;">
                    <div class="preview-content">
                        <div class="preview-sticker" id="previewStickerDisplay"></div>
                        <div class="preview-info">
                            <h4 id="previewStickerName">Sticker Name</h4>
                            <p id="previewStickerCategory">Category</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.populateCategories();
        this.updateRecentStickers();
    }

    populateCategories() {
        const tabsContainer = document.getElementById('stickerCategoryTabs');
        if (!tabsContainer) return;

        tabsContainer.innerHTML = '';
        
        Object.entries(STICKER_CATEGORIES).forEach(([key, category]) => {
            const tab = document.createElement('button');
            tab.className = `category-tab ${key === this.currentCategory ? 'active' : ''}`;
            tab.dataset.category = key;
            tab.innerHTML = `
                <i class="${category.icon}"></i>
                <span>${category.name}</span>
            `;
            tab.setAttribute('aria-label', `Select ${category.name} category`);
            tabsContainer.appendChild(tab);
        });
    }

    setupEventListeners() {
        // Category tab clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.category-tab')) {
                const tab = e.target.closest('.category-tab');
                const category = tab.dataset.category;
                this.selectCategory(category);
            }
        });

        // Sticker selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.sticker-item')) {
                const stickerItem = e.target.closest('.sticker-item');
                const stickerId = stickerItem.dataset.stickerId;
                this.selectSticker(stickerId);
            }
        });

        // Search functionality
        const searchInput = document.getElementById('stickerSearch');
        const clearSearch = document.getElementById('clearStickerSearch');
        
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value.toLowerCase();
                this.filterStickers();
                
                // Show/hide clear button
                if (clearSearch) {
                    clearSearch.style.display = this.searchQuery ? 'flex' : 'none';
                }
            });
        }

        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                searchInput.value = '';
                this.searchQuery = '';
                this.filterStickers();
                clearSearch.style.display = 'none';
                searchInput.focus();
            });
        }

        // Touch events for mobile
        this.setupTouchEvents();
    }

    setupTouchEvents() {
        let touchStartY = 0;
        const stickerGrid = document.getElementById('stickerGrid');
        
        if (stickerGrid) {
            stickerGrid.addEventListener('touchstart', (e) => {
                touchStartY = e.touches[0].clientY;
            }, { passive: true });

            stickerGrid.addEventListener('touchmove', (e) => {
                // Allow scrolling
            }, { passive: true });
        }
    }

    selectCategory(category) {
        this.currentCategory = category;
        
        // Update active tab
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.category === category);
        });

        this.loadCategory(category);
    }

    loadCategory(category) {
        const stickers = STICKER_DATA[category] || [];
        this.displayStickers(stickers);
    }

    displayStickers(stickers) {
        const grid = document.getElementById('stickerGrid');
        if (!grid) return;

        grid.innerHTML = '';

        if (stickers.length === 0) {
            grid.innerHTML = `
                <div class="no-stickers">
                    <i class="fas fa-search"></i>
                    <p>No stickers found</p>
                    <small>Try a different search term or category</small>
                </div>
            `;
            return;
        }

        stickers.forEach(sticker => {
            const stickerItem = document.createElement('div');
            stickerItem.className = 'sticker-item';
            stickerItem.dataset.stickerId = sticker.id;
            stickerItem.innerHTML = `
                <div class="sticker-preview-container">
                    <div class="sticker-svg">${sticker.svg}</div>
                </div>
                <div class="sticker-info">
                    <span class="sticker-name">${sticker.name}</span>
                </div>
            `;
            stickerItem.setAttribute('aria-label', `Select ${sticker.name} sticker`);
            grid.appendChild(stickerItem);
        });
    }

    filterStickers() {
        if (!this.searchQuery) {
            this.loadCategory(this.currentCategory);
            return;
        }

        // Search across all categories
        const allStickers = [];
        Object.values(STICKER_DATA).forEach(categoryStickers => {
            allStickers.push(...categoryStickers);
        });

        const filteredStickers = allStickers.filter(sticker => 
            sticker.name.toLowerCase().includes(this.searchQuery) ||
            sticker.category.toLowerCase().includes(this.searchQuery)
        );

        this.displayStickers(filteredStickers);
    }

    selectSticker(stickerId) {
        // Find sticker in all categories
        let selectedSticker = null;
        Object.values(STICKER_DATA).forEach(categoryStickers => {
            const found = categoryStickers.find(s => s.id === stickerId);
            if (found) selectedSticker = found;
        });

        if (!selectedSticker) return;

        this.selectedSticker = selectedSticker;
        this.showPreview(selectedSticker);
        this.addStickerToCanvas(selectedSticker);
        this.addToRecentStickers(selectedSticker);
    }

    showPreview(sticker) {
        const preview = document.getElementById('stickerPreview');
        const display = document.getElementById('previewStickerDisplay');
        const name = document.getElementById('previewStickerName');
        const category = document.getElementById('previewStickerCategory');

        if (preview && display && name && category) {
            display.innerHTML = sticker.svg;
            name.textContent = sticker.name;
            category.textContent = STICKER_CATEGORIES[sticker.category]?.name || sticker.category;
            preview.style.display = 'block';

            // Auto-hide preview after 3 seconds
            setTimeout(() => {
                preview.style.display = 'none';
            }, 3000);
        }
    }

    addStickerToCanvas(sticker) {
        if (!this.canvasManager || !this.canvasManager.canvas) return;

        // Create SVG element from string
        const svgString = sticker.svg;
        
        // Add sticker to canvas using Fabric.js
        fabric.loadSVGFromString(svgString, (objects, options) => {
            const stickerGroup = fabric.util.groupSVGElements(objects, options);
            
            // Set default properties
            stickerGroup.set({
                left: this.canvasManager.canvas.width / 2,
                top: this.canvasManager.canvas.height / 2,
                originX: 'center',
                originY: 'center',
                scaleX: 0.5,
                scaleY: 0.5,
                fill: '#333333', // Default color
                selectable: true,
                hasControls: true,
                hasBorders: true,
                // Add sticker data for identification
                stickerData: {
                    id: sticker.id,
                    name: sticker.name,
                    category: sticker.category,
                    originalSvg: sticker.svg
                }
            });

            // Add to canvas
            this.canvasManager.canvas.add(stickerGroup);
            this.canvasManager.canvas.setActiveObject(stickerGroup);
            this.canvasManager.canvas.renderAll();

            // Save state if available
            if (this.canvasManager.saveState) {
                this.canvasManager.saveState();
            }

            // Emit event
            this.emit('sticker:added', { sticker, fabricObject: stickerGroup });
        });
    }

    addToRecentStickers(sticker) {
        // Remove if already exists
        this.recentStickers = this.recentStickers.filter(s => s.id !== sticker.id);
        
        // Add to beginning
        this.recentStickers.unshift(sticker);
        
        // Keep only last 6
        this.recentStickers = this.recentStickers.slice(0, 6);
        
        // Save to localStorage
        this.saveRecentStickers();
        this.updateRecentStickers();
    }

    updateRecentStickers() {
        const section = document.getElementById('recentStickersSection');
        const grid = document.getElementById('recentStickersGrid');
        
        if (!section || !grid) return;

        if (this.recentStickers.length === 0) {
            section.style.display = 'none';
            return;
        }

        section.style.display = 'block';
        grid.innerHTML = '';

        this.recentStickers.forEach(sticker => {
            const stickerItem = document.createElement('div');
            stickerItem.className = 'sticker-item recent-item';
            stickerItem.dataset.stickerId = sticker.id;
            stickerItem.innerHTML = `
                <div class="sticker-preview-container">
                    <div class="sticker-svg">${sticker.svg}</div>
                </div>
            `;
            stickerItem.setAttribute('aria-label', `Select recent ${sticker.name} sticker`);
            grid.appendChild(stickerItem);
        });
    }

    loadRecentStickers() {
        try {
            const saved = localStorage.getItem('billboard-recent-stickers');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.warn('Failed to load recent stickers:', error);
            return [];
        }
    }

    saveRecentStickers() {
        try {
            localStorage.setItem('billboard-recent-stickers', JSON.stringify(this.recentStickers));
        } catch (error) {
            console.warn('Failed to save recent stickers:', error);
        }
    }

    // Event emitter functionality
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    // Public API
    getSelectedSticker() {
        return this.selectedSticker;
    }

    clearSelection() {
        this.selectedSticker = null;
        const preview = document.getElementById('stickerPreview');
        if (preview) preview.style.display = 'none';
    }
}
