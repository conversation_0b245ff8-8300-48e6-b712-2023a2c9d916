<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Button Click Target Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #testCanvas {
            border: 2px solid #000;
            margin: 10px 0;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .instructions h4 {
            margin-top: 0;
        }
        .instructions ol {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Delete Button Click Target Fix Test</h1>
        <p>This page tests the fix for delete button click target misalignment.</p>
        
        <div class="test-section">
            <h3>Test Canvas</h3>
            <canvas id="testCanvas" width="600" height="400"></canvas>
            <br>
            <button onclick="addTestText()">Add Text</button>
            <button onclick="addTestImage()">Add Test Image</button>
            <button onclick="clearCanvas()">Clear Canvas</button>
        </div>
        
        <div class="instructions">
            <h4>Testing Instructions:</h4>
            <ol>
                <li><strong>Add elements</strong>: Click "Add Text" or "Add Test Image" to add elements to the canvas</li>
                <li><strong>Select element</strong>: Click on any element to select it and show the delete button (red X)</li>
                <li><strong>Test delete button</strong>: Click directly on the red X button - it should delete the element immediately</li>
                <li><strong>Test precision</strong>: Try clicking on different parts of the X button - all areas should work</li>
                <li><strong>Test multiple elements</strong>: Add several elements and test delete buttons on each</li>
                <li><strong>Test at different zoom levels</strong>: Use browser zoom (Ctrl +/-) and test delete buttons</li>
            </ol>
            <p><strong>Expected Result</strong>: The delete button should work when clicking directly on the visible red X, with no offset or misalignment issues.</p>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="testResults"></div>
        </div>
    </div>

    <!-- Fabric.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <script>
        let canvas;
        let elementCounter = 0;
        
        // Initialize canvas
        function initCanvas() {
            canvas = new fabric.Canvas('testCanvas', {
                backgroundColor: '#ffffff',
                selection: true
            });
            
            logResult('Canvas initialized successfully', 'success');
            setupDeleteControls();
        }
        
        function setupDeleteControls() {
            // Add custom delete control (X button) with fixed click target
            function addDeleteControl(obj) {
                obj.controls.deleteControl = new fabric.Control({
                    x: 0.5,
                    y: -0.5,
                    offsetY: -8,
                    offsetX: 8,
                    cursorStyle: 'pointer',
                    // Fix click target alignment - use sizeX/sizeY instead of cornerSize
                    sizeX: 24,
                    sizeY: 24,
                    mouseUpHandler: function(eventData, transform) {
                        const target = transform.target;
                        canvas.remove(target);
                        canvas.renderAll();
                        logResult(`Element deleted successfully (${target.type})`, 'success');
                    },
                    render: function(ctx, left, top, styleOverride, fabricObject) {
                        const size = 20;
                        ctx.save();
                        ctx.translate(left, top);
                        ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
                        ctx.fillStyle = '#ff0000';
                        ctx.strokeStyle = '#ffffff';
                        ctx.lineWidth = 2;

                        // Draw circle background
                        ctx.beginPath();
                        ctx.arc(0, 0, size/2, 0, 2 * Math.PI);
                        ctx.fill();
                        ctx.stroke();

                        // Draw X
                        ctx.strokeStyle = '#ffffff';
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(-6, -6);
                        ctx.lineTo(6, 6);
                        ctx.moveTo(6, -6);
                        ctx.lineTo(-6, 6);
                        ctx.stroke();

                        ctx.restore();
                    }
                });
            }

            // Add delete control to all new objects
            canvas.on('object:added', function(e) {
                if (e.target && !e.target.isBackground) {
                    addDeleteControl(e.target);
                }
            });
            
            logResult('Delete controls setup completed', 'success');
        }
        
        function addTestText() {
            elementCounter++;
            const testText = new fabric.IText(`Test Text ${elementCounter}`, {
                left: 100 + (elementCounter * 30),
                top: 100 + (elementCounter * 30),
                fontFamily: 'Arial',
                fontSize: 20,
                fill: '#000000',
                editable: true,
                selectable: true
            });
            
            canvas.add(testText);
            canvas.setActiveObject(testText);
            canvas.renderAll();
            
            logResult(`Text element ${elementCounter} added`, 'info');
        }
        
        function addTestImage() {
            elementCounter++;
            // Create a simple colored rectangle as test image
            const testRect = new fabric.Rect({
                left: 200 + (elementCounter * 30),
                top: 150 + (elementCounter * 30),
                width: 80,
                height: 60,
                fill: `hsl(${elementCounter * 60}, 70%, 60%)`,
                selectable: true
            });
            
            canvas.add(testRect);
            canvas.setActiveObject(testRect);
            canvas.renderAll();
            
            logResult(`Test image ${elementCounter} added`, 'info');
        }
        
        function clearCanvas() {
            canvas.clear();
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
            elementCounter = 0;
            logResult('Canvas cleared', 'info');
        }
        
        function logResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(statusDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Initialize when page loads
        window.addEventListener('load', function() {
            if (typeof fabric === 'undefined') {
                logResult('Fabric.js not loaded!', 'error');
                return;
            }
            
            logResult(`Fabric.js loaded (version: ${fabric.version})`, 'success');
            initCanvas();
        });
    </script>
</body>
</html>
