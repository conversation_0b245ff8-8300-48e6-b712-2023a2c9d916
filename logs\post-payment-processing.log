2025-07-23 14:22:43 - [Order 7] Starting post-payment processing for order 7 (BM20250755DE3A)
2025-07-23 14:22:43 - [Order 7] Step 1: Generating billboard image...
2025-07-23 14:22:52 - [Order 7] Starting post-payment processing for order 7 (BM20250755DE3A)
2025-07-23 14:22:52 - [Order 7] Step 1: Generating billboard image...
2025-07-23 14:24:37 - [Order 7] Starting post-payment processing for order 7 (BM20250755DE3A)
2025-07-23 14:24:37 - [Order 7] Step 1: Generating billboard image...
2025-07-23 14:26:25 - [Order 7] Starting post-payment processing for order 7 (BM20250755DE3A)
2025-07-23 14:26:25 - [Order 7] Step 1: Generating billboard image...
2025-07-24 05:10:08 - [Order 7] Starting post-payment processing for order 7 (BM20250755DE3A)
2025-07-24 05:10:08 - [Order 7] Step 1: Generating billboard image...
2025-07-24 05:11:19 - [Order 7] Starting post-payment processing for order 7 (BM20250755DE3A)
2025-07-24 05:11:19 - [Order 7] Step 1: Generating billboard image...
2025-07-24 05:12:41 - [Order 7] Starting post-payment processing for order 7 (BM20250755DE3A)
2025-07-24 05:12:41 - [Order 7] Step 1: Generating billboard image...
2025-07-24 05:13:03 - [Order 7] ✗ Image generation failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'borges_media.image_generation_logs' doesn't exist
2025-07-24 05:13:03 - [Order 7] Step 2: Skipping image validation due to generation failure
2025-07-24 05:13:03 - [Order 7] Step 3: Skipping email delivery due to previous failures
2025-07-24 05:13:03 - [Order 7] ✗ Post-payment processing completed with errors for order 7
2025-07-24 05:13:03 - Order 7 - {"order_id":7,"order_number":"BM20250755DE3A","customer_email":"<EMAIL>","processing_time":"2025-07-24 05:13:03","results":{"order_id":7,"order_number":"BM20250755DE3A","image_generation":{"success":false,"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'borges_media.image_generation_logs' doesn't exist","attempts":4,"quality_type":"failed","all_attempts":[{"success":false,"method":"high_quality","attempt_number":1,"processing_time_ms":27,"error":"Generated image is invalid: Invalid image file","details":{"success":false,"error":"Generated image is invalid: Invalid image file"}},{"success":false,"method":"high_quality","attempt_number":2,"processing_time_ms":27,"error":"Generated image is invalid: Invalid image file","details":{"success":false,"error":"Generated image is invalid: Invalid image file"}},{"success":false,"method":"basic","attempt_number":3,"processing_time_ms":13,"error":"Generated image is invalid: Invalid image file","details":{"success":false,"error":"Generated image is invalid: Invalid image file"}},{"success":true,"image_path":"D:\\borgesmedia-billboard-maker-feature\/uploads\/billboards\/customer-e8ff6b8e\/billboard_emergency_BM20250755DE3A_2025-07-24_05-13-03_4bf02af1.txt","message":"Emergency text fallback created (GD extension not available)","generation_time":0.0011608600616455078,"attempt_number":4,"method":"emergency_text_fallback"}]},"image_validation":{"success":false,"error":"Skipped due to image generation failure"},"email_delivery":{"success":false,"error":"Skipped due to image generation or validation failure"},"overall_success":false,"processing_started_at":"2025-07-24 05:12:41","processing_completed_at":"2025-07-24 05:13:03"}}
2025-07-24 05:20:23 - [Order 8] Starting post-payment processing for order 8 (BM202507710556)
2025-07-24 05:20:23 - [Order 8] Step 1: Generating billboard image...
2025-07-24 05:20:44 - [Order 8] ✗ Image generation failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'generation_type' in 'field list'
2025-07-24 05:20:44 - [Order 8] Step 2: Skipping image validation due to generation failure
2025-07-24 05:20:44 - [Order 8] Step 3: Skipping email delivery due to previous failures
2025-07-24 05:20:44 - [Order 8] ✗ Post-payment processing completed with errors for order 8
2025-07-24 05:20:44 - Order 8 - {"order_id":"8","order_number":"BM202507710556","customer_email":"<EMAIL>","processing_time":"2025-07-24 05:20:44","results":{"order_id":"8","order_number":"BM202507710556","image_generation":{"success":false,"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'generation_type' in 'field list'","attempts":4,"quality_type":"failed","all_attempts":[{"success":false,"method":"high_quality","attempt_number":1,"processing_time_ms":4,"error":"Template ID not found in design data","details":{"success":false,"error":"Template ID not found in design data"}},{"success":false,"method":"high_quality","attempt_number":2,"processing_time_ms":17,"error":"Template ID not found in design data","details":{"success":false,"error":"Template ID not found in design data"}},{"success":false,"method":"basic","attempt_number":3,"processing_time_ms":17,"error":"Generated image is invalid: Invalid image file","details":{"success":false,"error":"Generated image is invalid: Invalid image file"}},{"success":true,"image_path":"D:\\borgesmedia-billboard-maker-feature\/uploads\/billboards\/customer-e6257616\/billboard_emergency_BM202507710556_2025-07-24_05-20-44_68c27b80.txt","message":"Emergency text fallback created (GD extension not available)","generation_time":0.004806995391845703,"attempt_number":4,"method":"emergency_text_fallback"}]},"image_validation":{"success":false,"error":"Skipped due to image generation failure"},"email_delivery":{"success":false,"error":"Skipped due to image generation or validation failure"},"overall_success":false,"processing_started_at":"2025-07-24 05:20:23","processing_completed_at":"2025-07-24 05:20:44"}}
