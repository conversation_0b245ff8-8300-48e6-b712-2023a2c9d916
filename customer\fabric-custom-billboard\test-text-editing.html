<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Editing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #testCanvas {
            border: 2px solid #000;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Text Editing Test</h1>
        <p>This page tests if the text editing functionality is working correctly.</p>
        
        <div class="test-section">
            <h3>Test Canvas</h3>
            <canvas id="testCanvas" width="400" height="200"></canvas>
            <br>
            <button onclick="addTestText()">Add Text</button>
            <button onclick="testTextEditing()">Test Text Editing</button>
            <button onclick="clearCanvas()">Clear Canvas</button>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h3>Instructions</h3>
            <ol>
                <li>Click "Add Text" to add a text object to the canvas</li>
                <li>Double-click on the text to edit it</li>
                <li>Try typing new text</li>
                <li>Click outside the text to finish editing</li>
                <li>Check if the text value changed</li>
            </ol>
        </div>
    </div>

    <!-- Fabric.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <script>
        let canvas;
        let testText;
        
        // Initialize canvas
        function initCanvas() {
            canvas = new fabric.Canvas('testCanvas', {
                backgroundColor: '#ffffff',
                selection: true
            });
            
            logResult('Canvas initialized successfully', 'success');
            
            // Add text editing event handlers
            canvas.on('text:editing:entered', function(e) {
                logResult('Text editing started - SUCCESS!', 'success');
            });
            
            canvas.on('text:editing:exited', function(e) {
                logResult('Text editing finished - SUCCESS!', 'success');
                logResult(`Final text value: "${e.target.text}"`, 'info');
            });
            
            // Double-click to edit text
            canvas.on('mouse:dblclick', function(options) {
                const target = options.target;
                if (target && (target.type === 'text' || target.type === 'i-text')) {
                    target.enterEditing();
                    target.selectAll();
                    logResult('Text editing activated via double-click', 'success');
                }
            });
        }
        
        function addTestText() {
            // Use fabric.IText for interactive text editing
            testText = new fabric.IText('Double-click to edit me!', {
                left: 200,
                top: 100,
                fontFamily: 'Arial',
                fontSize: 18,
                fill: '#000000',
                originX: 'center',
                originY: 'center',
                editable: true,
                selectable: true,
                evented: true,
                editingBorderColor: '#2563eb',
                cursorColor: '#2563eb',
                cursorWidth: 2
            });
            
            canvas.add(testText);
            canvas.setActiveObject(testText);
            canvas.renderAll();
            
            logResult(`Added IText object (type: ${testText.type})`, 'success');
        }
        
        function testTextEditing() {
            if (!testText) {
                logResult('No text object found. Please add text first.', 'error');
                return;
            }
            
            logResult(`Text object type: ${testText.type}`, 'info');
            logResult(`Text object editable: ${testText.editable}`, 'info');
            logResult(`Current text value: "${testText.text}"`, 'info');
            
            // Try to programmatically enter editing mode
            try {
                testText.enterEditing();
                logResult('Programmatically entered editing mode', 'success');
            } catch (error) {
                logResult(`Error entering editing mode: ${error.message}`, 'error');
            }
        }
        
        function clearCanvas() {
            canvas.clear();
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
            testText = null;
            logResult('Canvas cleared', 'info');
        }
        
        function logResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(statusDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Initialize when page loads
        window.addEventListener('load', function() {
            if (typeof fabric === 'undefined') {
                logResult('Fabric.js not loaded!', 'error');
                return;
            }
            
            logResult(`Fabric.js loaded (version: ${fabric.version})`, 'success');
            initCanvas();
        });
    </script>
</body>
</html>
