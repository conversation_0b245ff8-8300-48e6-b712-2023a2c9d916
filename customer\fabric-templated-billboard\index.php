<?php
// Set page variables for shared header
$pageTitle = 'Billboard Template Editor - Borges Media';
$headerTitle = 'Billboard Template Editor';
$headerSubtitle = 'Create stunning billboards with ease';
$headerIcon = 'fas fa-palette';

// Include shared header
include '../shared/header.php';
?>

    <!-- Fabric.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>

    <!-- Mobile-first responsive styles -->
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/forms.css">
    <link rel="stylesheet" href="assets/css/templates.css">
    <link rel="stylesheet" href="assets/css/panels.css">
    <link rel="stylesheet" href="assets/css/range-sliders.css">
    <link rel="stylesheet" href="assets/css/shadow-controls.css">
    <link rel="stylesheet" href="assets/css/text-fields.css">
    <link rel="stylesheet" href="assets/css/image-upload.css">
    <link rel="stylesheet" href="assets/css/background-modal.css">
    <link rel="stylesheet" href="assets/css/color-background-picker.css">
    <link rel="stylesheet" href="assets/css/sticker-interface.css">
    <link rel="stylesheet" href="assets/css/sticker-controls.css">
    <link rel="stylesheet" href="assets/css/responsive.css">

    <style>
        /* CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.5;
            color: #555;
            background-color: #f8f9fa;
            overflow-x: hidden;
            overscroll-behavior: none;
        }














    </style>
</head>
<body>
    <div class="editor-container">
        <!-- Main Content -->
        <main class="main-content">
            <!-- Control Panel -->
            <aside class="control-panel">
                <!-- Category Selection -->
                <div class="control-section">
                    <h3>Template Category</h3>
                    <div class="form-group">
                        <label for="categorySelect">Choose Category:</label>
                        <select id="categorySelect">
                            <option value="">Choose a category...</option>
                        </select>
                    </div>
                </div>

                <!-- Template Grid -->
                <div class="control-section">
                    <h3>Templates</h3>
                    <div id="templateGrid" class="template-grid">
                        <!-- Templates will be populated here -->
                    </div>
                </div>

                <!-- Text Fields Panel (Hidden by default) -->
                <div id="textEditingSection" class="control-section" style="display: none;">
                    <h3><i class="fas fa-edit"></i> Edit Text Content</h3>
                    <div id="textFieldsContainer" class="text-fields-container">
                        <!-- Text fields will be populated here when a template is loaded -->
                    </div>
                </div>

                <!-- Image Replacement Panel (Hidden by default) -->
                <div id="imageReplacementSection" class="control-section" style="display: none;">
                    <h3>🖼️ Replace Template Image</h3>
                    <div class="image-replacement-container">
                        <div class="current-image-info">
                            <p class="image-info-text">Current: Default template image</p>
                        </div>
                        <div class="image-upload-controls">
                            <input type="file" id="imageReplacementInput" accept="image/*" style="display: none;">
                            <button id="selectImageBtn" class="primary-btn">📁 Select New Image</button>
                            <div class="upload-info">
                                <small>Supported: JPG, PNG, GIF, WebP (max 5MB)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Background Change Panel (Hidden by default) -->
                <div id="backgroundChangeSection" class="control-section" style="display: none;">
                    <h3><i class="fas fa-image"></i> Change Background</h3>
                    <div class="background-change-container">
                        <div class="background-controls">
                            <button id="changeBackgroundBtn" class="primary-btn"><i class="fas fa-images"></i> Choose Background</button>
                            <div class="upload-info">
                                <small>Select from available background templates</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sticker Panel (Hidden by default) -->
                <div id="stickerSection" class="control-section" style="display: none;">
                    <h3><i class="fas fa-star"></i> Add Stickers</h3>
                    <div class="sticker-container">
                        <div class="sticker-controls">
                            <button id="addStickerBtn" class="primary-btn"><i class="fas fa-star"></i> Choose Sticker</button>
                            <div class="upload-info">
                                <small>Add icons, shapes, badges and decorative elements</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sticker Properties Panel (Hidden by default) -->
                <div id="stickerProperties" class="control-section sticker-properties" style="display: none;">
                    <h4>
                        <i class="fas fa-star"></i>
                        Sticker Properties
                    </h4>
                    <div id="stickerColorPickerContainer">
                        <!-- Color picker will be inserted here by StickerControlsManager -->
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button id="clearBtn" class="secondary">Clear</button>
                    <button id="exportBtn">Export</button>
                </div>
            </aside>

            <!-- Canvas Section -->
            <section class="canvas-section">
                <div class="canvas-container">
                    <canvas id="billboard-canvas"></canvas>
                </div>
            </section>

            <!-- Properties Panel -->
            <aside id="propertiesPanel" class="properties-panel">
                <div class="control-section">
                    <h3>Object Properties</h3>
                    <div id="objectProperties">
                        <!-- Object properties will be populated here -->
                    </div>
                </div>
            </aside>
        </main>
    </div>

    <!-- Background Selection Modal -->
    <div id="backgroundModal" class="background-modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle"><i class="fas fa-images"></i> Choose Background</h3>
                <button class="close-btn" id="closeBackgroundModal">&times;</button>
            </div>

            <!-- Background Type Tabs -->
            <div class="modal-tabs" id="modalTabs">
                <button class="modal-tab active" data-tab="images">
                    <i class="fas fa-images"></i>
                    <span>Images</span>
                </button>
                <button class="modal-tab" data-tab="colors">
                    <i class="fas fa-palette"></i>
                    <span>Colors</span>
                </button>
            </div>

            <div class="modal-body">
                <!-- Images Tab Content -->
                <div class="modal-tab-content active" id="imagesTabContent">
                    <div id="backgroundGrid" class="background-grid">
                        <!-- Background options will be populated here -->
                    </div>
                </div>

                <!-- Colors Tab Content -->
                <div class="modal-tab-content" id="colorsTabContent" style="display: none;">
                    <div id="colorBackgroundContainer">
                        <!-- Color picker will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sticker Selection Modal -->
    <div id="stickerModal" class="background-modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="stickerModalTitle"><i class="fas fa-star"></i> Add Stickers</h3>
                <button class="close-btn" id="closeStickerModal">&times;</button>
            </div>

            <div class="modal-body">
                <div id="stickerContainer">
                    <!-- Sticker interface will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Load template specifications -->
    <script src="template-specs.js"></script>

    <!-- Sticker Data -->
    <script src="sticker-data.js"></script>

    <!-- Color Background Manager -->
    <script src="assets/js/ColorBackgroundManager.js"></script>

    <!-- Sticker Manager -->
    <script src="assets/js/StickerManager.js"></script>

    <!-- Sticker Controls Manager -->
    <script src="assets/js/StickerControlsManager.js"></script>

    <!-- Load existing template manager -->
    <script src="assets/js/template-manager.js"></script>

    <!-- Load utilities -->
    <script src="assets/js/utils/TouchUtils.js"></script>
    <script src="assets/js/utils/ExportUtils.js"></script>

    <!-- Load core modules -->
    <script src="assets/js/core/ResponsiveManager.js"></script>
    <script src="assets/js/core/CanvasManager.js"></script>
    <script src="assets/js/core/EventHandler.js"></script>

    <!-- Load feature modules -->
    <script src="assets/js/modules/ImageReplacementManager.js"></script>
    <script src="assets/js/modules/BackgroundManager.js"></script>

    <!-- Load new refactored modules -->
    <script src="assets/js/modules/NotificationManager.js"></script>
    <script src="assets/js/modules/UIManager.js"></script>
    <script src="assets/js/modules/TemplateIntegrationManager.js"></script>
    <script src="assets/js/modules/TextCustomizationManager.js"></script>
    <script src="assets/js/modules/ImageUploadManager.js"></script>
    <script src="assets/js/modules/ExportManager.js"></script>
    <script src="assets/js/modules/EventCoordinator.js"></script>

    <!-- Load main application (now simplified coordinator) -->
    <script src="assets/js/main.js"></script>
</body>
</html>