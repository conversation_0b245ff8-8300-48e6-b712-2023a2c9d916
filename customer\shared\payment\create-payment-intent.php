<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to browser
ini_set('log_errors', 1);

require_once 'stripe-config.php';

setJsonHeaders();

// Set up error handler to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("Payment Intent Fatal Error: " . json_encode($error));
        if (!headers_sent()) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Server error occurred during payment intent creation'
            ]);
        }
    }
});

try {
    // Get order data from localStorage (passed via JavaScript)
    $rawInput = file_get_contents('php://input');
    error_log("Payment Intent Raw Input: " . $rawInput);

    $input = getJsonInput();

    // Debug: Log the input data
    error_log("Payment Intent Parsed Input: " . json_encode($input));

    // Validate that we have valid input
    if ($input === null) {
        error_log("Payment Intent: JSON decode failed for input: " . $rawInput);
        sendJsonResponse([
            'success' => false,
            'error' => 'Invalid JSON input received'
        ], 400);
    }
    
    // Calculate amount based on selected dates
    $selectedDates = $input['selectedDates'] ?? [];
    $billboardType = $input['billboardType'] ?? 'custom';

    // Ensure selectedDates is an array
    if (is_string($selectedDates)) {
        $selectedDates = json_decode($selectedDates, true) ?: [];
    }

    if (empty($selectedDates)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'No dates selected for billboard display'
        ], 400);
    }
    
    // Calculate total amount
    $amount = calculateBillboardAmount($selectedDates);
    
    if (!validatePaymentAmount($amount)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Invalid payment amount'
        ], 400);
    }
    
    // Get customer data from session
    session_start();
    $customerData = $_SESSION['checkout_customer_data'] ?? [];

    // Get design data and customer data from request
    $designData = $input['designData'] ?? null;
    $customerDataFromRequest = $input['customerData'] ?? null;

    // Create metadata for the payment
    $metadata = [
        'billboard_type' => $billboardType,
        'selected_dates' => json_encode($selectedDates),
        'duration_days' => count($selectedDates),
        'daily_rate' => getCurrentDailyRate(),
        'customer_name' => $customerData['name'] ?? ($customerDataFromRequest['customerName'] ?? ''),
        'customer_email' => $customerData['email'] ?? ($customerDataFromRequest['customerEmail'] ?? ''),
        'customer_phone' => $customerData['phone'] ?? ($customerDataFromRequest['customerPhone'] ?? ''),
        'has_design_data' => $designData ? 'true' : 'false'
    ];

    // Store design data separately if provided (metadata has size limits)
    if ($designData) {
        // Store design data in session for post-payment retrieval
        session_start();
        $_SESSION['payment_design_data'] = [
            'design_data' => $designData,
            'payment_intent_id' => null, // Will be set after payment intent creation
            'created_at' => date('Y-m-d H:i:s')
        ];
    }
    
    // Create payment intent
    $result = createStripePaymentIntent($amount, DEFAULT_CURRENCY, $metadata);

    if ($result['success']) {
        // Update design data with payment intent ID if it exists
        if (isset($_SESSION['payment_design_data'])) {
            $_SESSION['payment_design_data']['payment_intent_id'] = $result['payment_intent_id'];
        }

        sendJsonResponse([
            'success' => true,
            'paymentIntentId' => $result['payment_intent_id'],
            'clientSecret' => $result['client_secret'],
            'amount' => $amount,
            'currency' => DEFAULT_CURRENCY,
            'formattedAmount' => formatAmountForDisplay($amount)
        ]);
    } else {
        sendJsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 500);
    }
    
} catch (Exception $e) {
    error_log("Payment Intent Creation Exception: " . $e->getMessage());
    error_log("Payment Intent Creation Trace: " . $e->getTraceAsString());

    logPaymentActivity('create_payment_intent_error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);

    sendJsonResponse([
        'success' => false,
        'error' => 'An unexpected error occurred while creating payment intent: ' . $e->getMessage()
    ], 500);
}
?>
