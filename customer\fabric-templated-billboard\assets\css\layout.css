/* ========================================
   LAYOUT AND CONTAINER STYLES
   ======================================== */

/* Container and Layout */
.editor-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 100vw;
}

/* Header styles removed - using shared header */

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    gap: var(--spacing-md);
}

/* Canvas Container */
.canvas-section {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    order: 2;
}

.canvas-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background: #fafafa;
    border-radius: var(--border-radius);
    position: relative;
}

#billboard-canvas {
    border: 2px solid #007bff;
    border-radius: 4px;
    max-width: 100%;
    height: auto;
}

/* Control Panel */
.control-panel {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    order: 1;
}

.control-section {
    margin-bottom: var(--spacing-lg);
}

.control-section:last-child {
    margin-bottom: 0;
}

.control-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Properties Panel */
.properties-panel {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    display: none;
    order: 3;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.action-buttons button {
    flex: 1;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ccc;
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
