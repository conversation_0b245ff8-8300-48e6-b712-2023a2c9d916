// ========================================
// CUSTOMIZATION MANAGEMENT SYSTEM
// ========================================

class CustomizationManager {
    constructor() {
        this.textCustomizationPanel = document.getElementById('textCustomizationPanel');
        this.backgroundModal = document.getElementById('backgroundModal');
        this.currentTextElement = null;
        this.panelOverlay = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createPanelOverlay();
    }

    setupEventListeners() {
        // Text customization form elements
        const textContent = document.getElementById('textContent');
        const textColor = document.getElementById('textColor');
        const fontSize = document.getElementById('fontSize');
        const fontFamily = document.getElementById('fontFamily');
        const fontWeight = document.getElementById('fontWeight');
        const textShadow = document.getElementById('textShadow');

        // Font size slider
        if (fontSize) {
            fontSize.addEventListener('input', () => {
                document.getElementById('fontSizeValue').textContent = fontSize.value + 'px';
            });
        }

        // Background modal close on overlay click
        if (this.backgroundModal) {
            this.backgroundModal.addEventListener('click', (e) => {
                if (e.target === this.backgroundModal) {
                    this.closeBackgroundModal();
                }
            });
        }

        // Panel overlay close
        if (this.panelOverlay) {
            this.panelOverlay.addEventListener('click', () => {
                this.hideTextCustomization();
            });
        }
    }

    createPanelOverlay() {
        this.panelOverlay = document.createElement('div');
        this.panelOverlay.className = 'panel-overlay hidden';
        document.body.appendChild(this.panelOverlay);
    }

    // Show text customization panel
    showTextCustomization(textElement) {
        if (!textElement || !textElement.classList.contains('text-element')) {
            return;
        }

        this.currentTextElement = textElement;
        
        // Populate form with current values
        this.populateTextForm(textElement);
        
        // Show panel and overlay
        this.textCustomizationPanel.classList.remove('hidden');
        this.panelOverlay.classList.remove('hidden');
    }

    // Hide text customization panel
    hideTextCustomization() {
        this.textCustomizationPanel.classList.add('hidden');
        this.panelOverlay.classList.add('hidden');
        this.currentTextElement = null;
    }

    // Populate text customization form
    populateTextForm(textElement) {
        const computedStyle = window.getComputedStyle(textElement);
        
        // Text content
        document.getElementById('textContent').value = textElement.textContent;
        
        // Color (convert rgb to hex if needed)
        const color = this.rgbToHex(computedStyle.color) || '#ffffff';
        document.getElementById('textColor').value = color;
        
        // Font size (remove 'px' suffix)
        const fontSize = parseInt(computedStyle.fontSize) || 32;
        document.getElementById('fontSize').value = fontSize;
        document.getElementById('fontSizeValue').textContent = fontSize + 'px';
        
        // Font family
        const fontFamily = computedStyle.fontFamily.replace(/['"]/g, '').split(',')[0] || 'Arial';
        document.getElementById('fontFamily').value = fontFamily;
        
        // Font weight
        const fontWeight = computedStyle.fontWeight || '400';
        document.getElementById('fontWeight').value = fontWeight;
        
        // Text shadow
        const hasTextShadow = computedStyle.textShadow && computedStyle.textShadow !== 'none';
        document.getElementById('textShadow').checked = hasTextShadow;
    }

    // Apply text changes
    applyTextChanges() {
        if (!this.currentTextElement) {
            return;
        }

        const textContent = document.getElementById('textContent').value;
        const textColor = document.getElementById('textColor').value;
        const fontSize = document.getElementById('fontSize').value;
        const fontFamily = document.getElementById('fontFamily').value;
        const fontWeight = document.getElementById('fontWeight').value;
        const textShadow = document.getElementById('textShadow').checked;

        // Update text content
        this.currentTextElement.textContent = textContent;

        // Enforce single-line behavior
        this.enforceSingleLineText(this.currentTextElement);

        // Update styles
        const styles = {
            color: textColor,
            fontSize: fontSize + 'px',
            fontFamily: fontFamily,
            fontWeight: fontWeight,
            textShadow: textShadow ? '1px 1px 2px rgba(0,0,0,0.8)' : 'none'
        };

        window.canvasManager.updateTextStyles(this.currentTextElement, styles);
        
        // Hide panel
        this.hideTextCustomization();
    }

    // Show background selection modal
    showBackgroundModal() {
        const category = window.templateManager.currentCategory;
        if (!category) {
            alert('Please select a template first');
            return;
        }

        const backgrounds = window.templateManager.getBackgroundsForCategory(category);
        this.populateBackgroundGrid(backgrounds);
        
        this.backgroundModal.classList.remove('hidden');
    }

    // Close background modal
    closeBackgroundModal() {
        this.backgroundModal.classList.add('hidden');
    }

    // Populate background selection grid
    populateBackgroundGrid(backgrounds) {
        const grid = document.getElementById('backgroundGrid');
        if (!grid) return;

        grid.innerHTML = '';

        if (!backgrounds || backgrounds.length === 0) {
            grid.innerHTML = '<p>No background images available for this category.</p>';
            return;
        }

        backgrounds.forEach(bg => {
            const option = document.createElement('div');
            option.className = 'background-option';
            option.style.backgroundImage = `url('${bg.url}')`;
            option.title = bg.name;
            
            option.addEventListener('click', () => {
                this.selectBackground(bg.url);
            });

            grid.appendChild(option);
        });
    }

    // Select a background
    selectBackground(imageUrl) {
        window.canvasManager.changeBackground(imageUrl);
        this.closeBackgroundModal();
    }

    // Handle image upload
    handleImageUpload() {
        const fileInput = document.getElementById('imageUpload');
        if (!fileInput) return;

        fileInput.click();
        
        fileInput.onchange = (e) => {
            const file = e.target.files[0];
            if (!file) return;

            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Image file is too large. Please select an image under 5MB.');
                return;
            }

            // Create file reader
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageUrl = e.target.result;
                window.canvasManager.updateImageSource(imageUrl);
            };
            reader.readAsDataURL(file);
        };
    }

    // Utility function to convert RGB to Hex
    rgbToHex(rgb) {
        if (!rgb || rgb === 'rgba(0, 0, 0, 0)') return '#ffffff';
        
        const result = rgb.match(/\d+/g);
        if (!result || result.length < 3) return '#ffffff';
        
        const r = parseInt(result[0]);
        const g = parseInt(result[1]);
        const b = parseInt(result[2]);
        
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // Get current text element
    getCurrentTextElement() {
        return this.currentTextElement;
    }

    // Enforce single-line text behavior
    enforceSingleLineText(element) {
        if (element) {
            element.style.whiteSpace = 'nowrap';
            element.style.overflow = 'visible';
            element.style.textOverflow = 'clip';
            element.style.wordWrap = 'normal';
            element.style.wordBreak = 'normal';
            element.style.hyphens = 'none';
        }
    }
}

// Global functions for HTML onclick handlers
function closeTextCustomization() {
    window.customizationManager.hideTextCustomization();
}

function applyTextChanges() {
    window.customizationManager.applyTextChanges();
}

function closeBackgroundModal() {
    window.customizationManager.closeBackgroundModal();
}

function showBackgroundModal() {
    window.customizationManager.showBackgroundModal();
}

function handleImageUpload() {
    window.customizationManager.handleImageUpload();
}

// Create global instance
window.customizationManager = new CustomizationManager();
