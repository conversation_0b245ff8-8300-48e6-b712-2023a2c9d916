<?php
// Include the shared header component
$pageTitle = "Templated Billboard Designer - Borges Media";
$headerTitle = "Templated Billboard Maker";
$headerSubtitle = "Choose a template, customize it, and create your perfect billboard";
$headerIcon = "fas fa-palette";
include '../shared/header.php';
?>

    <!-- Templated Billboard Specific Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif !important;
            background: #f0f0f0 !important;
            min-height: 100vh !important;
            color: #333 !important;
            margin: 0 !important;
            /* DO NOT override padding - shared header sets padding-top: 80px */
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            /* Header spacing is handled by shared header body padding */
        }



        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        /* Left Panel - Controls */
        .controls-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            height: fit-content;
        }

        .control-section {
            margin-bottom: 25px;
        }

        .control-section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* CF7-style Dropdown - Mimicking custom-billboard design */
        .category-dropdown {
            width: 100% !important;
            padding: 12px 15px !important;
            border: 1px solid #007cba !important;
            border-radius: 4px !important;
            font-size: 1rem !important;
            background: white !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            box-sizing: border-box !important;
            font-family: 'Inter', sans-serif !important;
        }

        .category-dropdown:focus {
            outline: 2px solid #007cba !important;
            outline-offset: 2px !important;
            border-color: #005a87 !important;
            box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1) !important;
        }

        .category-dropdown:hover {
            border-color: #005a87 !important;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        /* CF7-style Template Cards - Mimicking custom-billboard design */
        .template-card {
            background: white !important;
            border: 1px solid #007cba !important;
            border-radius: 4px !important;
            padding: 12px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            text-align: center !important;
            box-sizing: border-box !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .template-card:hover {
            border-color: #005a87 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            background: #f8f9fa !important;
        }

        .template-card.selected {
            border-color: #007cba !important;
            background: rgba(0, 124, 186, 0.1) !important;
            box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2) !important;
        }

        .template-card:focus {
            outline: 2px solid #007cba !important;
            outline-offset: 2px !important;
        }

        .template-preview {
            width: 100%;
            height: 60px;
            background: #f1f3f4;
            border-radius: 5px;
            margin-bottom: 8px;
            background-size: cover;
            background-position: center;
        }

        .template-name {
            font-size: 0.85rem;
            font-weight: 500;
            color: #555;
        }

        /* Global single-line text enforcement for all text elements */
        .billboard-canvas .text-element,
        .billboard-canvas .cf7-text-element,
        .billboard-canvas .cf7-draggable-text,
        .billboard-canvas .cf7-editable-content,
        .billboard-canvas [class*="text-element"],
        .billboard-canvas [class*="text"] {
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: clip !important;
            word-wrap: normal !important;
            word-break: normal !important;
            hyphens: none !important;
        }

        /* CF7-style Text Fields Panel - Mimicking custom-billboard design */
        .text-fields-panel {
            background: white !important;
            border: 1px solid #007cba !important;
            border-radius: 4px !important;
            padding: 20px !important;
            margin-bottom: 20px !important;
            display: none !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            box-sizing: border-box !important;
        }

        .text-fields-panel.visible {
            display: block !important;
        }

        .text-fields-panel h3 {
            color: #007cba !important;
            margin-bottom: 15px !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
        }

        /* CF7-style Text Field Groups - Mimicking custom-billboard design */
        .text-field-group {
            margin-bottom: 15px !important;
            padding: 15px !important;
            background: #f8f9fa !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important;
            box-sizing: border-box !important;
        }

        .text-field-label {
            font-weight: 600 !important;
            margin-bottom: 8px !important;
            color: #333 !important;
            display: block !important;
            font-size: 0.9rem !important;
        }

        .text-field-input {
            width: 100% !important;
            padding: 10px 12px !important;
            border: 1px solid #007cba !important;
            border-radius: 4px !important;
            font-size: 1rem !important;
            transition: all 0.3s ease !important;
            box-sizing: border-box !important;
            font-family: 'Inter', sans-serif !important;
        }

        .text-field-input:focus {
            outline: 2px solid #007cba !important;
            outline-offset: 2px !important;
            border-color: #005a87 !important;
            box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1) !important;
        }

        .text-field-input:hover {
            border-color: #005a87 !important;
        }

        /* CF7-style Customize Button - Mimicking custom-billboard design */
        .text-customize-btn {
            background: #007cba !important;
            color: white !important;
            border: none !important;
            padding: 8px 16px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 0.9rem !important;
            font-weight: 500 !important;
            margin-top: 8px !important;
            transition: all 0.3s ease !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            box-sizing: border-box !important;
            text-decoration: none !important;
        }

        .text-customize-btn:hover {
            background: #005a87 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        .text-customize-btn:focus {
            outline: 2px solid #007cba !important;
            outline-offset: 2px !important;
        }

        .text-customize-btn:active {
            transform: translateY(0) !important;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
        }

        /* Right Panel - Canvas */
        .canvas-panel {
            display: flex;
            flex-direction: column;
        }

        .canvas-container {
            flex: 1;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 300px;
        }

        .billboard-canvas {
            /* PROPER RESPONSIVE SOLUTION: Fluid width + aspect ratio */
            width: 100%;
            max-width: 800px;
            aspect-ratio: 2 / 1;
            background: white;
            border-radius: 10px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;

            /* Ensure minimum size for usability */
            min-width: 280px;
            min-height: 140px;

            /* Enable container queries for responsive font sizing */
            container-type: inline-size;
            container-name: billboard-canvas;
        }

        .canvas-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
        }

        .text-element {
            position: absolute;
            cursor: pointer;
            user-select: none;
            transition: all 0.2s ease;
            padding: 2px 4px;
            border-radius: 3px;
            min-width: 20px;
            min-height: 20px;
            line-height: 1.2;

            /* FORCE SINGLE LINE TEXT - NO WRAPPING */
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: clip !important;
            word-wrap: normal !important;
            word-break: normal !important;
            hyphens: none !important;

            /* Font size will be set dynamically by JavaScript based on template specs */
        }

        /* CF7-style text elements - ensure single line behavior */
        .cf7-text-element,
        .cf7-draggable-text,
        .cf7-editable-content {
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: clip !important;
            word-wrap: normal !important;
            word-break: normal !important;
            hyphens: none !important;
        }

        .text-element:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .text-element.selected {
            background: rgba(102, 126, 234, 0.2);
            outline: 2px dashed #667eea;
        }

        /* Touch-friendly improvements for mobile */
        @media (max-width: 768px) {
            .text-element {
                min-width: 30px;
                min-height: 30px;
                padding: 4px 6px;
                /* Maintain single-line behavior on mobile */
                white-space: nowrap !important;
                overflow: visible !important;
            }

            .image-element {
                min-width: 40px;
                min-height: 40px;
            }

            /* Larger touch targets */
            .template-card {
                min-height: 80px;
                padding: 15px;
            }

            .text-customize-btn {
                padding: 10px 16px;
                font-size: 1rem;
            }
        }

        /* Export Mode Styles - HIGH QUALITY EXPORT SYSTEM */
        .billboard-canvas[data-exporting="true"] {
            /* Force original dimensions during export for consistent quality */
            width: 800px !important;
            height: 400px !important;
            max-width: none !important;
            min-width: none !important;
            min-height: none !important;
            aspect-ratio: unset !important;
            transform: none !important;
            contain: none !important;
            overflow: visible !important;
            position: relative !important;

            /* Ensure clean export without responsive scaling */
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        /* Hide UI elements during export */
        .billboard-canvas[data-exporting="true"] .text-customize-btn,
        .billboard-canvas[data-exporting="true"] .selected {
            display: none !important;
        }

        /* Ensure text elements maintain their positioning during export */
        .billboard-canvas[data-exporting="true"] .text-element {
            /* Text elements use percentage positioning, so they scale correctly */
            position: absolute;
            /* Maintain single-line behavior during export */
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: clip !important;
        }

        /* Ensure image elements maintain their positioning during export */
        .billboard-canvas[data-exporting="true"] .image-element {
            /* Image elements use percentage positioning, so they scale correctly */
            position: absolute;
        }

        .image-element {
            position: absolute;
            cursor: pointer;
            border-radius: 5px;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .image-element img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-element:hover {
            outline: 2px solid #667eea;
        }

        .image-element.selected {
            outline: 3px solid #667eea;
        }

        /* CF7-style Action Buttons - Mimicking custom-billboard design */
        .action-buttons {
            display: flex !important;
            gap: 12px !important;
            margin-top: 20px !important;
            flex-wrap: wrap !important;
        }

        .btn {
            padding: 12px 18px !important;
            border: 1px solid #007cba !important;
            border-radius: 4px !important;
            font-size: 14px !important;
            font-weight: bold !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 8px !important;
            flex: 1 !important;
            min-width: 150px !important;
            min-height: 44px !important;
            box-sizing: border-box !important;
            text-decoration: none !important;
            white-space: nowrap !important;
        }

        .btn-primary {
            background: #007cba !important;
            color: white !important;
            border-color: #007cba !important;
        }

        .btn-primary:hover:not(:disabled) {
            background: #005a87 !important;
            border-color: #005a87 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        .btn-secondary {
            background: #6c757d !important;
            color: white !important;
            border-color: #6c757d !important;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #5a6268 !important;
            border-color: #5a6268 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        .btn-success {
            background: #28a745 !important;
            color: white !important;
            border-color: #28a745 !important;
        }

        .btn-success:hover:not(:disabled) {
            background: #218838 !important;
            border-color: #218838 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        /* CF7-style Button States - Mimicking custom-billboard design */
        .btn:disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
            transform: none !important;
            background: #ccc !important;
            border-color: #ccc !important;
            color: #666 !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            background: #ccc !important;
            border-color: #ccc !important;
            box-shadow: none !important;
        }

        .btn:focus {
            outline: 2px solid #007cba !important;
            outline-offset: 2px !important;
        }

        .btn:active:not(:disabled) {
            transform: translateY(0) !important;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
        }

        /* Loading state */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
            padding: 20px;
            text-align: center;
        }

        /* CF7-style Modal Styles - Mimicking custom-billboard design */
        .modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0,0,0,0.5) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            z-index: 999999 !important;
            animation: fadeIn 0.3s ease !important;
        }

        .modal.hidden {
            display: none !important;
        }

        .modal-content {
            background: white !important;
            border-radius: 8px !important;
            max-width: 90vw !important;
            max-height: 90vh !important;
            width: 800px !important;
            overflow: hidden !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
            animation: slideIn 0.3s ease !important;
            margin: 0 !important;
            position: relative !important;
        }

        /* CF7-style Modal Header - Mimicking custom-billboard design */
        .modal-header {
            padding: 20px 25px !important;
            border-bottom: 1px solid #ddd !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            background: #007cba !important;
            color: white !important;
            flex-shrink: 0 !important;
        }

        .modal-header h3 {
            margin: 0 !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            font-size: 1.2rem !important;
            font-weight: 600 !important;
        }

        .modal-close {
            background: none !important;
            border: none !important;
            font-size: 1.5rem !important;
            cursor: pointer !important;
            color: white !important;
            padding: 5px !important;
            border-radius: 4px !important;
            transition: all 0.2s ease !important;
            width: 32px !important;
            height: 32px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2) !important;
        }

        .modal-close:focus {
            outline: 2px solid white !important;
            outline-offset: 2px !important;
        }

        /* CF7-style Modal Body - Mimicking custom-billboard design */
        .modal-body {
            padding: 25px !important;
            max-height: 60vh !important;
            overflow-y: auto !important;
            flex: 1 !important;
        }

        .background-grid {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
            gap: 15px !important;
        }

        .background-option {
            aspect-ratio: 2/1 !important;
            background-size: cover !important;
            background-position: center !important;
            border: 1px solid #007cba !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .background-option:hover {
            border-color: #005a87 !important;
            transform: scale(1.02) !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
        }

        .background-option:focus {
            outline: 2px solid #007cba !important;
            outline-offset: 2px !important;
        }

        .background-option.selected {
            border-color: #007cba !important;
            box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.3) !important;
        }

        /* CF7-style Text Customization Panel - Mimicking custom-billboard design */
        .customization-panel {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            background: white !important;
            border-radius: 8px !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
            z-index: 999999 !important;
            width: 400px !important;
            max-width: 90vw !important;
            max-height: 90vh !important;
            overflow: hidden !important;
            animation: slideIn 0.3s ease !important;
            border: none !important;
        }

        .customization-panel.hidden {
            display: none !important;
        }

        .panel-header {
            padding: 20px 25px !important;
            border-bottom: 1px solid #ddd !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            background: #007cba !important;
            color: white !important;
            flex-shrink: 0 !important;
        }

        .panel-header h3 {
            margin: 0 !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            font-size: 1.2rem !important;
            font-weight: 600 !important;
        }

        .panel-close {
            background: none !important;
            border: none !important;
            font-size: 1.5rem !important;
            cursor: pointer !important;
            color: white !important;
            padding: 5px !important;
            border-radius: 4px !important;
            transition: all 0.2s ease !important;
            width: 32px !important;
            height: 32px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .panel-close:hover {
            background: rgba(255,255,255,0.2) !important;
        }

        .panel-close:focus {
            outline: 2px solid white !important;
            outline-offset: 2px !important;
        }

        /* CF7-style Panel Body and Form Groups - Mimicking custom-billboard design */
        .panel-body {
            padding: 25px !important;
            max-height: 60vh !important;
            overflow-y: auto !important;
            flex: 1 !important;
        }

        .form-group {
            margin-bottom: 20px !important;
        }

        .form-group label {
            display: block !important;
            margin-bottom: 8px !important;
            font-weight: 600 !important;
            color: #333 !important;
            font-size: 0.9rem !important;
        }

        .form-group input,
        .form-group select {
            width: 100% !important;
            padding: 10px 12px !important;
            border: 1px solid #007cba !important;
            border-radius: 4px !important;
            font-size: 1rem !important;
            transition: all 0.3s ease !important;
            box-sizing: border-box !important;
            font-family: 'Inter', sans-serif !important;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: 2px solid #007cba !important;
            outline-offset: 2px !important;
            border-color: #005a87 !important;
            box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1) !important;
        }

        .form-group input:hover,
        .form-group select:hover {
            border-color: #005a87 !important;
        }

        /* CF7-style Form Controls - Mimicking custom-billboard design */
        .form-group input[type="range"] {
            padding: 0 !important;
            height: 6px !important;
            background: #ddd !important;
            border-radius: 3px !important;
            appearance: none !important;
        }

        .form-group input[type="range"]::-webkit-slider-thumb {
            appearance: none !important;
            width: 18px !important;
            height: 18px !important;
            background: #007cba !important;
            border-radius: 50% !important;
            cursor: pointer !important;
        }

        .form-group input[type="checkbox"] {
            width: auto !important;
            margin-right: 8px !important;
            accent-color: #007cba !important;
        }

        .form-actions {
            display: flex !important;
            gap: 10px !important;
            margin-top: 25px !important;
            padding-top: 20px !important;
            border-top: 1px solid #ddd !important;
            background: #f8f9fa !important;
            margin: 25px -25px -25px -25px !important;
            padding: 20px 25px !important;
        }

        .form-actions .btn {
            flex: 1 !important;
            margin: 0 !important;
        }

        /* CF7-style Responsive Design - Mimicking custom-billboard approach */

        /* Mobile First (320px+) - CF7 Style */
        .main-content {
            grid-template-columns: 1fr !important;
            gap: 15px !important;
            padding: 15px !important;
        }

        .canvas-container {
            min-height: 200px !important;
            padding: 10px !important;
        }

        /* Ensure canvas is always visible on mobile */
        .billboard-canvas {
            width: 100% !important;
            max-width: none !important;
        }

        .templates-grid {
            grid-template-columns: 1fr !important;
            gap: 10px !important;
        }

        .action-buttons {
            flex-direction: column !important;
            gap: 8px !important;
        }

        .action-buttons .btn {
            width: 100% !important;
            justify-content: center !important;
            min-width: auto !important;
            min-height: 48px !important;
            text-align: center !important;
            margin: 1px 0 !important;
        }

        .text-fields-panel {
            order: -1 !important;
        }

        /* CF7-style Large Mobile (481px+) */
        @media (min-width: 481px) {
            .templates-grid {
                grid-template-columns: repeat(2, 1fr) !important;
            }

            .canvas-container {
                min-height: 250px !important;
                padding: 15px !important;
            }

            .action-buttons {
                flex-direction: row !important;
                flex-wrap: wrap !important;
            }

            .action-buttons .btn {
                flex: 1 !important;
                min-width: 140px !important;
            }
        }

        /* CF7-style Tablet Portrait (768px+) */
        @media (min-width: 768px) {
            .templates-grid {
                grid-template-columns: repeat(3, 1fr) !important;
            }

            .canvas-container {
                min-height: 300px !important;
                padding: 20px !important;
            }

            .action-buttons .btn {
                min-height: 44px !important;
            }

            /* Modal responsive adjustments */
            .modal-content {
                width: 600px !important;
            }

            .customization-panel {
                width: 450px !important;
            }
        }

        /* CF7-style Tablet Landscape / Small Desktop (1024px+) */
        @media (min-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr 2fr !important;
                gap: 30px !important;
                padding: 30px !important;
            }

            .canvas-container {
                min-height: 400px !important;
            }

            .text-fields-panel {
                order: 0 !important;
            }

            .action-buttons {
                flex-direction: row !important;
                gap: 12px !important;
            }

            .action-buttons .btn {
                width: auto !important;
                min-width: 150px !important;
                flex: 1 !important;
            }

            /* Desktop modal sizes */
            .modal-content {
                width: 800px !important;
            }

            .customization-panel {
                width: 500px !important;
            }
        }

        /* CF7-style Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .container {
                padding: 20px !important;
            }

            .canvas-container {
                min-height: 500px !important;
            }

            .templates-grid {
                grid-template-columns: repeat(4, 1fr) !important;
            }
        }



        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }


    </style>
</head>
<body>
    <div class="container">
        <div class="main-content">
            <!-- Left Panel - Controls -->
            <div class="controls-panel">
                <!-- Category Selection -->
                <div class="control-section">
                    <h3><i class="fas fa-list"></i> Select Category</h3>
                    <select class="category-dropdown" id="categorySelect">
                        <option value="">Choose a category...</option>
                    </select>
                </div>

                <!-- Template Selection -->
                <div class="control-section">
                    <h3><i class="fas fa-images"></i> Choose Template</h3>
                    <div class="templates-grid" id="templatesGrid">
                        <div class="loading">Select a category to view templates</div>
                    </div>
                </div>

                <!-- Text Fields Panel (Hidden initially) -->
                <div class="text-fields-panel" id="textFieldsPanel">
                    <h3><i class="fas fa-font"></i> Edit Text Content</h3>
                    <div id="textFieldsContainer">
                        <!-- Text fields will be populated here -->
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn btn-secondary" id="changeBackgroundBtn" disabled>
                        <i class="fas fa-image"></i> Change Background
                    </button>
                    <button class="btn btn-secondary" id="uploadImageBtn" disabled>
                        <i class="fas fa-upload"></i> Upload Image
                    </button>
                </div>
            </div>

            <!-- Right Panel - Canvas -->
            <div class="canvas-panel">
                <div class="canvas-container">
                    <div class="billboard-canvas" id="billboardCanvas" data-width="800" data-height="400">
                        <div class="canvas-background" id="canvasBackground"></div>
                        <!-- Text and image elements will be added here dynamically -->
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" id="exportBtn" disabled>
                        <i class="fas fa-download"></i> Export Billboard
                    </button>
                    <button class="btn btn-success" id="proceedPaymentBtn" disabled onclick="openUnifiedCheckout()">
                        <i class="fas fa-shopping-cart"></i> Checkout
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->

    <!-- Background Selection Modal -->
    <div id="backgroundModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-image"></i> Choose Background</h3>
                <button class="modal-close" onclick="closeBackgroundModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="background-grid" id="backgroundGrid">
                    <!-- Background options will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Text Customization Panel -->
    <div id="textCustomizationPanel" class="customization-panel hidden">
        <div class="panel-header">
            <h3><i class="fas fa-font"></i> Text Customization</h3>
            <button class="panel-close" onclick="closeTextCustomization()">&times;</button>
        </div>
        <div class="panel-body">
            <div class="form-group">
                <label>Text Content:</label>
                <input type="text" id="textContent" placeholder="Enter text...">
            </div>
            <div class="form-group">
                <label>Font Color:</label>
                <input type="color" id="textColor" value="#ffffff">
            </div>
            <div class="form-group">
                <label>Font Size:</label>
                <input type="range" id="fontSize" min="12" max="72" value="32">
                <span id="fontSizeValue">32px</span>
            </div>
            <div class="form-group">
                <label>Font Family:</label>
                <select id="fontFamily">
                    <option value="Inter">Inter</option>
                    <option value="Dancing Script">Dancing Script</option>
                    <option value="Roboto">Roboto</option>
                    <option value="Open Sans">Open Sans</option>
                    <option value="Lato">Lato</option>
                    <option value="Montserrat">Montserrat</option>
                    <option value="Poppins">Poppins</option>
                    <option value="Playfair Display">Playfair Display</option>
                    <option value="Merriweather">Merriweather</option>
                    <option value="Source Sans Pro">Source Sans Pro</option>
                    <option value="Nunito">Nunito</option>
                    <option value="Raleway">Raleway</option>
                    <option value="Oswald">Oswald</option>
                    <option value="Pacifico">Pacifico</option>
                    <option value="Lobster">Lobster</option>
                </select>
            </div>
            <div class="form-group">
                <label>Font Weight:</label>
                <select id="fontWeight">
                    <option value="300">Light</option>
                    <option value="400">Normal</option>
                    <option value="500">Medium</option>
                    <option value="600">Semi Bold</option>
                    <option value="700">Bold</option>
                    <option value="900">Black</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="textShadow"> Add Text Shadow
                </label>
            </div>
            <div class="form-actions">
                <button class="btn btn-primary" onclick="applyTextChanges()">Apply Changes</button>
                <button class="btn btn-secondary" onclick="closeTextCustomization()">Cancel</button>
            </div>
        </div>
    </div>

    <!-- File Upload Input (Hidden) -->
    <input type="file" id="imageUpload" accept="image/*" style="display: none;">



    <!-- Include Unified Checkout Modal -->
    <link rel="stylesheet" href="../shared/checkout-modal.css">
    <?php include '../shared/checkout-modal.php'; ?>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/html-to-image@1.11.13/dist/html-to-image.js"></script>

    <!-- Template Specifications -->
    <script src="template-specs.js"></script>

    <!-- Main Application Scripts -->
    <script src="assets/js/template-manager.js"></script>
    <script src="assets/js/canvas-manager.js"></script>
    <script src="assets/js/customization-manager.js"></script>
    <script src="assets/js/export-manager.js"></script>
    <script src="assets/js/app.js"></script>

    <!-- Unified Checkout System -->
    <script src="../shared/order-data-manager.js"></script>
    <script src="../shared/high-quality-image-generator.js"></script>
    <script src="../shared/checkout-modal.js"></script>



</body>
</html>