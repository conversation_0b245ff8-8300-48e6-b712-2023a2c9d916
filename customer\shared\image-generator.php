<?php
// Billboard Image Generation System

require_once dirname(__DIR__, 2) . '/config/database.php';
require_once dirname(__DIR__, 2) . '/config/storage.php';
require_once dirname(__DIR__, 2) . '/config/payment.php';

class BillboardImageGenerator {
    private $pdo;
    private $order;
    
    public function __construct($orderId) {
        $this->pdo = getDBConnection();
        $this->loadOrder($orderId);
    }
    
    private function loadOrder($orderId) {
        $stmt = $this->pdo->prepare("
            SELECT o.*, bi.image_path as existing_image_path
            FROM orders o
            LEFT JOIN billboard_images bi ON o.id = bi.order_id
            WHERE o.id = ?
        ");
        $stmt->execute([$orderId]);
        $this->order = $stmt->fetch();
        
        if (!$this->order) {
            throw new Exception("Order not found: $orderId");
        }
    }
    
    /**
     * Generate billboard image based on order type
     */
    public function generateImage() {
        try {
            // Check if image already exists
            if ($this->order['existing_image_path'] && file_exists($this->order['existing_image_path'])) {
                return [
                    'success' => true,
                    'image_path' => $this->order['existing_image_path'],
                    'message' => 'Image already exists'
                ];
            }
            
            $imagePath = null;
            
            if ($this->order['billboard_type'] === 'templated') {
                $imagePath = $this->generateTemplatedImage();
            } else {
                $imagePath = $this->generateCustomImage();
            }
            
            if ($imagePath) {
                // Save image record to database
                $this->saveImageRecord($imagePath);
                
                // Update order with image path
                $this->updateOrderWithImage($imagePath);
                
                return [
                    'success' => true,
                    'image_path' => $imagePath,
                    'message' => 'Image generated successfully'
                ];
            } else {
                throw new Exception('Failed to generate image');
            }
            
        } catch (Exception $e) {
            error_log("Image generation failed for order {$this->order['id']}: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate image for templated billboard
     */
    private function generateTemplatedImage() {
        // For templated billboards, we would typically:
        // 1. Load the template data from the order or session
        // 2. Apply the customizations
        // 3. Render the final image
        
        // For now, create a placeholder image
        return $this->createPlaceholderImage('templated');
    }
    
    /**
     * Generate image for custom billboard
     */
    private function generateCustomImage() {
        // For custom billboards, we would typically:
        // 1. Load the custom design data
        // 2. Render the canvas elements
        // 3. Generate the final image
        
        // For now, create a placeholder image
        return $this->createPlaceholderImage('custom');
    }
    
    /**
     * Create a placeholder image for demonstration
     */
    private function createPlaceholderImage($type) {
        // Create customer directory
        $customerDir = getCustomerBillboardPath($this->order['id']);
        
        // Generate filename
        $filename = generateBillboardFilename($this->order['id']);
        $imagePath = $customerDir . '/' . $filename;
        
        // Create a sample billboard image
        $width = 1920;
        $height = 1080;
        
        $image = imagecreatetruecolor($width, $height);
        
        // Colors
        $backgroundColor = imagecolorallocate($image, 45, 62, 80); // Dark blue-gray
        $textColor = imagecolorallocate($image, 255, 255, 255); // White
        $accentColor = imagecolorallocate($image, 40, 167, 69); // Green
        
        // Fill background
        imagefill($image, 0, 0, $backgroundColor);
        
        // Add border
        $borderColor = imagecolorallocate($image, 108, 117, 125);
        imagerectangle($image, 10, 10, $width - 11, $height - 11, $borderColor);
        imagerectangle($image, 20, 20, $width - 21, $height - 21, $borderColor);
        
        // Add title
        $title = strtoupper($type) . ' BILLBOARD';
        $titleFont = 5; // Built-in font
        $titleWidth = imagefontwidth($titleFont) * strlen($title);
        $titleX = ($width - $titleWidth) / 2;
        $titleY = 100;
        imagestring($image, $titleFont, $titleX, $titleY, $title, $textColor);
        
        // Add customer name
        $customerName = $this->order['customer_name'];
        $nameFont = 4;
        $nameWidth = imagefontwidth($nameFont) * strlen($customerName);
        $nameX = ($width - $nameWidth) / 2;
        $nameY = 200;
        imagestring($image, $nameFont, $nameX, $nameY, $customerName, $accentColor);
        
        // Add order number
        $orderNumber = 'Order: ' . $this->order['order_number'];
        $orderFont = 3;
        $orderWidth = imagefontwidth($orderFont) * strlen($orderNumber);
        $orderX = ($width - $orderWidth) / 2;
        $orderY = 300;
        imagestring($image, $orderFont, $orderX, $orderY, $orderNumber, $textColor);
        
        // Add display dates
        if ($this->order['booking_start_date']) {
            $startDate = date('M j, Y', strtotime($this->order['booking_start_date']));
            $endDate = date('M j, Y', strtotime($this->order['booking_end_date']));
            
            $dateText = 'Display Period: ' . $startDate;
            if ($this->order['booking_start_date'] !== $this->order['booking_end_date']) {
                $dateText .= ' - ' . $endDate;
            }
            
            $dateFont = 3;
            $dateWidth = imagefontwidth($dateFont) * strlen($dateText);
            $dateX = ($width - $dateWidth) / 2;
            $dateY = 400;
            imagestring($image, $dateFont, $dateX, $dateY, $dateText, $textColor);
        }
        
        // Add Borges Media branding
        $brandText = 'BORGES MEDIA';
        $brandFont = 2;
        $brandWidth = imagefontwidth($brandFont) * strlen($brandText);
        $brandX = ($width - $brandWidth) / 2;
        $brandY = $height - 100;
        imagestring($image, $brandFont, $brandX, $brandY, $brandText, $accentColor);
        
        // Add decorative elements
        $this->addDecorativeElements($image, $width, $height, $accentColor);
        
        // Save image
        if (imagepng($image, $imagePath, 8)) {
            imagedestroy($image);
            
            // Create thumbnail
            $thumbnailPath = THUMBNAILS_PATH . '/' . generateThumbnailFilename($this->order['id']);
            createThumbnail($imagePath, $thumbnailPath);
            
            return $imagePath;
        } else {
            imagedestroy($image);
            throw new Exception('Failed to save image');
        }
    }
    
    /**
     * Add decorative elements to the image
     */
    private function addDecorativeElements($image, $width, $height, $color) {
        // Add some geometric shapes for visual appeal
        
        // Top left corner decoration
        imagefilledellipse($image, 150, 150, 100, 100, $color);
        imagefilledellipse($image, 150, 150, 60, 60, imagecolorallocate($image, 45, 62, 80));
        
        // Top right corner decoration
        imagefilledellipse($image, $width - 150, 150, 100, 100, $color);
        imagefilledellipse($image, $width - 150, 150, 60, 60, imagecolorallocate($image, 45, 62, 80));
        
        // Bottom decorative line
        imagefilledrectangle($image, 100, $height - 200, $width - 100, $height - 180, $color);
    }
    
    /**
     * Save image record to database
     */
    private function saveImageRecord($imagePath) {
        $fileInfo = validateImageFile($imagePath);
        
        if (!$fileInfo['valid']) {
            throw new Exception('Generated image is invalid: ' . $fileInfo['error']);
        }
        
        $stmt = $this->pdo->prepare("
            INSERT INTO billboard_images (
                order_id, image_filename, image_path, image_size_bytes,
                image_width, image_height, image_format, generation_data
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $generationData = json_encode([
            'generated_at' => date('Y-m-d H:i:s'),
            'billboard_type' => $this->order['billboard_type'],
            'customer_name' => $this->order['customer_name'],
            'order_number' => $this->order['order_number']
        ]);
        
        $stmt->execute([
            $this->order['id'],
            basename($imagePath),
            $imagePath,
            $fileInfo['size'],
            $fileInfo['width'],
            $fileInfo['height'],
            $fileInfo['format'],
            $generationData
        ]);
    }
    
    /**
     * Update order with image path
     */
    private function updateOrderWithImage($imagePath) {
        $stmt = $this->pdo->prepare("
            UPDATE orders 
            SET billboard_image_path = ?, image_generated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$imagePath, $this->order['id']]);
        
        // Add to order history
        $stmt = $this->pdo->prepare("
            INSERT INTO order_history (order_id, status_from, status_to, notes)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $this->order['id'],
            $this->order['status'],
            $this->order['status'],
            'Billboard image generated successfully'
        ]);
    }
    
    /**
     * Get generated image path
     */
    public function getImagePath() {
        return $this->order['billboard_image_path'] ?? null;
    }
    
    /**
     * Get image URL for web access
     */
    public function getImageUrl() {
        $imagePath = $this->getImagePath();
        if ($imagePath) {
            // Convert file path to web URL
            $relativePath = str_replace(dirname(__DIR__, 2), '', $imagePath);
            return str_replace('\\', '/', $relativePath);
        }
        return null;
    }
}

/**
 * Generate image for order (can be called from external scripts)
 */
function generateBillboardImage($orderId) {
    try {
        $generator = new BillboardImageGenerator($orderId);
        return $generator->generateImage();
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
?>
