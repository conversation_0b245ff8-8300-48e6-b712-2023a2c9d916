<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shadow Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-weight: bold;
            font-size: 0.9em;
        }
        .control-group input, .control-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.active {
            background: #28a745;
        }
        #testCanvas {
            border: 2px solid #000;
            margin: 10px 0;
        }
        .range-display {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Shadow Functionality Test</h1>
        <p>This page tests all shadow controls and functionality.</p>
        
        <div class="test-section">
            <h3>Test Canvas</h3>
            <canvas id="testCanvas" width="600" height="300"></canvas>
            <br>
            <button onclick="addTestText()">Add Text</button>
            <button id="shadowToggle" onclick="toggleShadow()">Toggle Shadow</button>
            <button onclick="clearCanvas()">Clear Canvas</button>
        </div>
        
        <div class="test-section">
            <h3>Shadow Controls</h3>
            <div class="controls-grid">
                <div class="control-group">
                    <label for="shadowType">Shadow Type:</label>
                    <select id="shadowType" onchange="updateShadow()">
                        <option value="glow">Glow (No Offset)</option>
                        <option value="drop">Drop Shadow</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="shadowColor">Shadow Color:</label>
                    <input type="color" id="shadowColor" value="#000000" onchange="updateShadow()">
                </div>
                
                <div class="control-group">
                    <label for="shadowBlur">Blur: <span id="shadowBlurValue" class="range-display">5</span></label>
                    <input type="range" id="shadowBlur" min="1" max="30" value="5" oninput="updateShadow(); updateRangeDisplay(this)">
                </div>
                
                <div class="control-group">
                    <label for="shadowOpacity">Opacity: <span id="shadowOpacityValue" class="range-display">100%</span></label>
                    <input type="range" id="shadowOpacity" min="0" max="100" value="100" oninput="updateShadow(); updateRangeDisplay(this)">
                </div>
            </div>
            
            <div id="offsetControls" style="display: none;">
                <h4>Offset Controls (Drop Shadow Only)</h4>
                <div class="controls-grid">
                    <div class="control-group">
                        <label for="shadowOffsetX">Offset X: <span id="shadowOffsetXValue" class="range-display">2</span></label>
                        <input type="range" id="shadowOffsetX" min="-20" max="20" value="2" oninput="updateShadow(); updateRangeDisplay(this)">
                    </div>
                    
                    <div class="control-group">
                        <label for="shadowOffsetY">Offset Y: <span id="shadowOffsetYValue" class="range-display">2</span></label>
                        <input type="range" id="shadowOffsetY" min="-20" max="20" value="2" oninput="updateShadow(); updateRangeDisplay(this)">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h3>Test Instructions</h3>
            <ol>
                <li>Click "Add Text" to add a text object</li>
                <li>Click "Toggle Shadow" to enable/disable shadow</li>
                <li>Change shadow type between "Glow" and "Drop Shadow"</li>
                <li>Adjust blur, opacity, color, and offset values</li>
                <li>Verify that offset controls only show for drop shadow</li>
                <li>Test that glow has no offset, drop shadow uses offset values</li>
            </ol>
        </div>
    </div>

    <!-- Fabric.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <script>
        let canvas;
        let testText;
        let shadowEnabled = false;
        
        // Initialize canvas
        function initCanvas() {
            canvas = new fabric.Canvas('testCanvas', {
                backgroundColor: '#ffffff',
                selection: true
            });
            
            logResult('Canvas initialized successfully', 'success');
            
            // Initialize shadow type handler
            const shadowType = document.getElementById('shadowType');
            shadowType.addEventListener('change', () => {
                const isDropShadow = shadowType.value === 'drop';
                const offsetControls = document.getElementById('offsetControls');
                offsetControls.style.display = isDropShadow ? 'block' : 'none';
                
                // Apply defaults based on type
                if (shadowType.value === 'glow') {
                    document.getElementById('shadowBlur').value = 8;
                    document.getElementById('shadowOffsetX').value = 0;
                    document.getElementById('shadowOffsetY').value = 0;
                    logResult('Switched to glow mode - no offset', 'info');
                } else {
                    document.getElementById('shadowBlur').value = 3;
                    document.getElementById('shadowOffsetX').value = 2;
                    document.getElementById('shadowOffsetY').value = 2;
                    logResult('Switched to drop shadow mode - offset enabled', 'info');
                }
                
                updateRangeDisplays();
                updateShadow();
            });
        }
        
        function addTestText() {
            testText = new fabric.IText('Shadow Test Text', {
                left: 300,
                top: 150,
                fontFamily: 'Arial',
                fontSize: 24,
                fill: '#000000',
                originX: 'center',
                originY: 'center',
                editable: true,
                selectable: true
            });
            
            canvas.add(testText);
            canvas.setActiveObject(testText);
            canvas.renderAll();
            
            logResult('Test text added', 'success');
        }
        
        function toggleShadow() {
            shadowEnabled = !shadowEnabled;
            const toggleBtn = document.getElementById('shadowToggle');
            toggleBtn.classList.toggle('active', shadowEnabled);
            
            if (shadowEnabled) {
                updateShadow();
                logResult('Shadow enabled', 'success');
            } else {
                if (testText) {
                    testText.set('shadow', null);
                    canvas.renderAll();
                }
                logResult('Shadow disabled', 'info');
            }
        }
        
        function updateShadow() {
            if (!testText || !shadowEnabled) return;
            
            const shadowColor = document.getElementById('shadowColor').value;
            const shadowBlur = parseInt(document.getElementById('shadowBlur').value);
            const shadowOffsetX = parseInt(document.getElementById('shadowOffsetX').value);
            const shadowOffsetY = parseInt(document.getElementById('shadowOffsetY').value);
            const shadowOpacity = parseInt(document.getElementById('shadowOpacity').value) / 100;
            const shadowType = document.getElementById('shadowType').value;
            
            // Determine offset based on shadow type
            let offsetX, offsetY;
            if (shadowType === 'glow') {
                offsetX = 0;
                offsetY = 0;
            } else {
                offsetX = shadowOffsetX;
                offsetY = shadowOffsetY;
            }
            
            const shadow = new fabric.Shadow({
                color: shadowColor,
                blur: shadowBlur,
                offsetX: offsetX,
                offsetY: offsetY,
                opacity: shadowOpacity
            });
            
            testText.set('shadow', shadow);
            canvas.renderAll();
            
            logResult(`Shadow updated: ${shadowType}, blur: ${shadowBlur}, offset: (${offsetX}, ${offsetY}), opacity: ${Math.round(shadowOpacity * 100)}%`, 'success');
        }
        
        function updateRangeDisplay(rangeInput) {
            const valueDisplay = document.getElementById(rangeInput.id + 'Value');
            if (valueDisplay) {
                let value = rangeInput.value;
                if (rangeInput.id === 'shadowOpacity') {
                    value += '%';
                }
                valueDisplay.textContent = value;
            }
        }
        
        function updateRangeDisplays() {
            ['shadowBlur', 'shadowOffsetX', 'shadowOffsetY', 'shadowOpacity'].forEach(id => {
                const input = document.getElementById(id);
                if (input) updateRangeDisplay(input);
            });
        }
        
        function clearCanvas() {
            canvas.clear();
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
            testText = null;
            shadowEnabled = false;
            document.getElementById('shadowToggle').classList.remove('active');
            logResult('Canvas cleared', 'info');
        }
        
        function logResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(statusDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Initialize when page loads
        window.addEventListener('load', function() {
            if (typeof fabric === 'undefined') {
                logResult('Fabric.js not loaded!', 'error');
                return;
            }
            
            logResult(`Fabric.js loaded (version: ${fabric.version})`, 'success');
            initCanvas();
            updateRangeDisplays();
        });
    </script>
</body>
</html>
