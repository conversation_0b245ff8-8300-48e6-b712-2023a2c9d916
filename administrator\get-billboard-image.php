<?php
// Get Billboard Image API for Admin Dashboard

session_start();
require_once dirname(__DIR__) . '/config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

try {
    $orderId = $_GET['order_id'] ?? null;
    
    if (!$orderId || !is_numeric($orderId)) {
        throw new Exception('Invalid order ID');
    }
    
    $pdo = getDBConnection();
    
    // Get order and image information
    $stmt = $pdo->prepare("
        SELECT o.order_number, o.customer_name, o.billboard_type,
               bi.image_path, bi.image_filename, bi.created_at as image_created_at
        FROM orders o
        LEFT JOIN billboard_images bi ON o.id = bi.order_id
        WHERE o.id = ?
    ");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Order not found');
    }
    
    if (!$order['image_path'] || !file_exists($order['image_path'])) {
        throw new Exception('Billboard image not found');
    }
    
    // Convert file path to web URL
    $imagePath = $order['image_path'];
    $webRoot = dirname(__DIR__);
    $relativePath = str_replace($webRoot, '', $imagePath);
    $imageUrl = str_replace('\\', '/', $relativePath);
    
    // Ensure URL starts with /
    if (!str_starts_with($imageUrl, '/')) {
        $imageUrl = '/' . $imageUrl;
    }
    
    echo json_encode([
        'success' => true,
        'image_url' => $imageUrl,
        'image_filename' => $order['image_filename'],
        'order_number' => $order['order_number'],
        'customer_name' => $order['customer_name'],
        'billboard_type' => $order['billboard_type'],
        'image_created_at' => $order['image_created_at']
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
