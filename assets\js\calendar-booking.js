// ========================================
// CALENDAR BOOKING SYSTEM
// ========================================

class CalendarBooking {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.currentDate = new Date();
        this.selectedDates = [];
        this.bookedDates = new Set();
        this.unavailableDates = new Set();
        
        // Configuration
        this.options = {
            minDate: new Date(),
            maxDate: new Date(Date.now() + (365 * 24 * 60 * 60 * 1000)), // 1 year from now
            minBookingDays: 1,
            maxBookingDays: 30,
            allowMultipleSelection: true,
            ...options
        };
        
        this.init();
        this.loadBookedDates();
    }
    
    init() {
        this.render();
        this.attachEventListeners();
    }
    
    render() {
        const calendarHTML = `
            <div class="calendar-booking">
                <div class="calendar-header">
                    <button type="button" class="calendar-nav-btn" id="prevMonth">&lt;</button>
                    <h3 class="calendar-title" id="calendarTitle"></h3>
                    <button type="button" class="calendar-nav-btn" id="nextMonth">&gt;</button>
                </div>
                <div class="calendar-weekdays">
                    <div class="weekday">Sun</div>
                    <div class="weekday">Mon</div>
                    <div class="weekday">Tue</div>
                    <div class="weekday">Wed</div>
                    <div class="weekday">Thu</div>
                    <div class="weekday">Fri</div>
                    <div class="weekday">Sat</div>
                </div>
                <div class="calendar-days" id="calendarDays"></div>
                <div class="calendar-legend">
                    <div class="legend-item">
                        <span class="legend-color available"></span>
                        <span>Available</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color selected"></span>
                        <span>Selected</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color booked"></span>
                        <span>Booked</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color unavailable"></span>
                        <span>Unavailable</span>
                    </div>
                </div>
                <div class="calendar-selection-info">
                    <p id="selectionInfo">Select dates for your billboard display</p>
                    <div class="selected-dates-list" id="selectedDatesList"></div>
                </div>
            </div>
        `;
        
        this.container.innerHTML = calendarHTML;
        this.updateCalendar();
    }
    
    attachEventListeners() {
        document.getElementById('prevMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            this.updateCalendar();
        });
        
        document.getElementById('nextMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            this.updateCalendar();
        });
        
        document.getElementById('calendarDays').addEventListener('click', (e) => {
            if (e.target.classList.contains('calendar-day') && !e.target.classList.contains('disabled')) {
                this.handleDateClick(e.target);
            }
        });
    }
    
    updateCalendar() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        
        // Update title
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        document.getElementById('calendarTitle').textContent = `${monthNames[month]} ${year}`;
        
        // Generate calendar days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());
        
        const daysContainer = document.getElementById('calendarDays');
        daysContainer.innerHTML = '';
        
        for (let i = 0; i < 42; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            
            const dayElement = this.createDayElement(date, month);
            daysContainer.appendChild(dayElement);
        }
        
        this.updateSelectionInfo();
    }
    
    createDayElement(date, currentMonth) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.textContent = date.getDate();
        dayElement.dataset.date = this.formatDate(date);
        
        // Add classes based on date status
        if (date.getMonth() !== currentMonth) {
            dayElement.classList.add('other-month');
        }
        
        if (this.isDateBeforeMin(date) || this.isDateAfterMax(date)) {
            dayElement.classList.add('disabled');
        }
        
        if (this.isDateBooked(date)) {
            dayElement.classList.add('booked');
            dayElement.title = 'This date is already booked';
        }
        
        if (this.isDateUnavailable(date)) {
            dayElement.classList.add('unavailable');
            dayElement.title = 'This date is unavailable';
        }
        
        if (this.isDateSelected(date)) {
            dayElement.classList.add('selected');
        }
        
        if (this.isToday(date)) {
            dayElement.classList.add('today');
        }
        
        return dayElement;
    }
    
    handleDateClick(dayElement) {
        const dateStr = dayElement.dataset.date;
        const date = new Date(dateStr);
        
        if (dayElement.classList.contains('booked') || 
            dayElement.classList.contains('unavailable') ||
            dayElement.classList.contains('disabled')) {
            return;
        }
        
        if (this.isDateSelected(date)) {
            this.removeSelectedDate(date);
            dayElement.classList.remove('selected');
        } else {
            if (this.selectedDates.length < this.options.maxBookingDays) {
                this.addSelectedDate(date);
                dayElement.classList.add('selected');
            } else {
                alert(`You can select a maximum of ${this.options.maxBookingDays} days.`);
            }
        }
        
        this.updateSelectionInfo();
        this.notifySelectionChange();
    }
    
    addSelectedDate(date) {
        this.selectedDates.push(new Date(date));
        this.selectedDates.sort((a, b) => a - b);
    }
    
    removeSelectedDate(date) {
        this.selectedDates = this.selectedDates.filter(d => 
            this.formatDate(d) !== this.formatDate(date)
        );
    }
    
    isDateSelected(date) {
        return this.selectedDates.some(d => 
            this.formatDate(d) === this.formatDate(date)
        );
    }
    
    isDateBooked(date) {
        return this.bookedDates.has(this.formatDate(date));
    }
    
    isDateUnavailable(date) {
        return this.unavailableDates.has(this.formatDate(date));
    }
    
    isDateBeforeMin(date) {
        return date < this.options.minDate;
    }
    
    isDateAfterMax(date) {
        return date > this.options.maxDate;
    }
    
    isToday(date) {
        const today = new Date();
        return this.formatDate(date) === this.formatDate(today);
    }
    
    formatDate(date) {
        return date.toISOString().split('T')[0];
    }
    
    updateSelectionInfo() {
        const infoElement = document.getElementById('selectionInfo');
        const listElement = document.getElementById('selectedDatesList');
        
        if (this.selectedDates.length === 0) {
            infoElement.textContent = 'Select dates for your billboard display';
            listElement.innerHTML = '';
        } else {
            infoElement.textContent = `${this.selectedDates.length} day(s) selected`;
            
            const datesList = this.selectedDates.map(date => {
                const options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
                return `<span class="selected-date-item">${date.toLocaleDateString('en-US', options)}</span>`;
            }).join('');
            
            listElement.innerHTML = datesList;
        }
    }
    
    async loadBookedDates() {
        try {
            const response = await fetch('shared/get-booked-dates.php');
            const data = await response.json();
            
            if (data.success) {
                this.bookedDates = new Set(data.bookedDates);
                this.unavailableDates = new Set(data.unavailableDates || []);
                this.updateCalendar();
            }
        } catch (error) {
            console.error('Failed to load booked dates:', error);
        }
    }
    
    notifySelectionChange() {
        // Dispatch custom event for other components to listen to
        const event = new CustomEvent('calendarSelectionChange', {
            detail: {
                selectedDates: this.selectedDates.map(d => this.formatDate(d)),
                isValid: this.isSelectionValid()
            }
        });
        document.dispatchEvent(event);
    }
    
    isSelectionValid() {
        return this.selectedDates.length >= this.options.minBookingDays;
    }
    
    getSelectedDates() {
        return this.selectedDates.map(d => this.formatDate(d));
    }
    
    clearSelection() {
        this.selectedDates = [];
        this.updateCalendar();
        this.notifySelectionChange();
    }
    
    setBookedDates(bookedDates) {
        this.bookedDates = new Set(bookedDates);
        this.updateCalendar();
    }
    
    setUnavailableDates(unavailableDates) {
        this.unavailableDates = new Set(unavailableDates);
        this.updateCalendar();
    }
}

// Global calendar instance
let billboardCalendar = null;

// Initialize calendar when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const calendarContainer = document.getElementById('billboardCalendar');
    if (calendarContainer) {
        billboardCalendar = new CalendarBooking('billboardCalendar', {
            minBookingDays: 1,
            maxBookingDays: 7, // Allow up to 7 days selection
            allowMultipleSelection: true
        });
    }
});

// Export for use in other scripts
window.CalendarBooking = CalendarBooking;
