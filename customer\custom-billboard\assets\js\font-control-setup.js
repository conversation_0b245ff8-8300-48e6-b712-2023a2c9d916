// ========================================
// FONT CONTROL SETUP
// ========================================

// Extend CF7TextEditor class with font control methods
Object.assign(CF7TextEditor.prototype, {
    setupFontControls() {
        // Connect to existing HTML font controls instead of using shortcode conversion
        this.connectExistingFontControls();
    },

    connectExistingFontControls() {
        // Connect font family dropdown
        const fontFamily = document.getElementById('cf7-font-family');
        if (fontFamily) {
            this.fontControls.fontFamily = fontFamily;
            fontFamily.addEventListener('change', (e) => {
                this.updateSelectedFont('fontFamily', e.target.value);
            });
        }

        // Connect font size input
        const fontSize = document.getElementById('cf7-font-size');
        if (fontSize) {
            this.fontControls.fontSize = fontSize;
            fontSize.addEventListener('input', (e) => {
                this.updateSelectedFont('fontSize', e.target.value + 'px');
            });
        }

        // Connect font style buttons
        const boldBtn = document.getElementById('cf7-font-bold');
        if (boldBtn) {
            this.fontControls.bold = boldBtn;
            boldBtn.addEventListener('click', () => {
                this.toggleSelectedFont('fontWeight', 'bold', 'normal');
            });
        }

        const italicBtn = document.getElementById('cf7-font-italic');
        if (italicBtn) {
            this.fontControls.italic = italicBtn;
            italicBtn.addEventListener('click', () => {
                this.toggleSelectedFont('fontStyle', 'italic', 'normal');
            });
        }

        // Connect text alignment buttons
        const alignLeft = document.getElementById('cf7-align-left');
        if (alignLeft) {
            this.fontControls.alignLeft = alignLeft;
            alignLeft.addEventListener('click', () => {
                console.log('Left align clicked');
                this.setTextAlignment('left');
            });
        }

        const alignCenter = document.getElementById('cf7-align-center');
        if (alignCenter) {
            this.fontControls.alignCenter = alignCenter;
            alignCenter.addEventListener('click', () => {
                console.log('Center align clicked');
                this.setTextAlignment('center');
            });
        }

        const alignRight = document.getElementById('cf7-align-right');
        if (alignRight) {
            this.fontControls.alignRight = alignRight;
            alignRight.addEventListener('click', () => {
                console.log('Right align clicked');
                this.setTextAlignment('right');
            });
        }

        const alignJustify = document.getElementById('cf7-align-justify');
        if (alignJustify) {
            this.fontControls.alignJustify = alignJustify;
            alignJustify.addEventListener('click', () => {
                console.log('Justify align clicked');
                this.setTextAlignment('justify');
            });
        }

        // Connect font color picker
        const fontColor = document.getElementById('cf7-font-color');
        if (fontColor) {
            this.fontControls.color = fontColor;
            fontColor.addEventListener('input', (e) => {
                this.updateSelectedFont('color', e.target.value);
            });
        }

        // Connect text shadow controls
        const shadowToggle = document.getElementById('cf7-text-shadow-toggle');
        if (shadowToggle) {
            this.fontControls.textShadow = shadowToggle;
            shadowToggle.addEventListener('click', () => {
                this.toggleTextShadow();
            });
        }

        // Connect shadow color picker
        const shadowColor = document.getElementById('cf7-shadow-color');
        if (shadowColor) {
            this.fontControls.shadowColor = shadowColor;
            shadowColor.addEventListener('input', (e) => {
                this.updateTextShadow('color', e.target.value);
            });
        }

        // Connect shadow blur input
        const shadowBlur = document.getElementById('cf7-shadow-blur');
        if (shadowBlur) {
            this.fontControls.shadowBlur = shadowBlur;
            shadowBlur.addEventListener('input', (e) => {
                this.updateTextShadow('blur', e.target.value);
            });
        }

        // Connect shadow offset X input
        const shadowOffsetX = document.getElementById('cf7-shadow-offset-x');
        if (shadowOffsetX) {
            this.fontControls.shadowOffsetX = shadowOffsetX;
            shadowOffsetX.addEventListener('input', (e) => {
                this.updateTextShadow('offsetX', e.target.value);
            });
        }

        // Connect shadow offset Y input
        const shadowOffsetY = document.getElementById('cf7-shadow-offset-y');
        if (shadowOffsetY) {
            this.fontControls.shadowOffsetY = shadowOffsetY;
            shadowOffsetY.addEventListener('input', (e) => {
                this.updateTextShadow('offsetY', e.target.value);
            });
        }

        // Connect shadow opacity slider
        const shadowOpacity = document.getElementById('cf7-shadow-opacity-slider');
        const opacityValue = document.getElementById('cf7-opacity-value');
        if (shadowOpacity) {
            this.fontControls.shadowOpacity = shadowOpacity;
            shadowOpacity.addEventListener('input', (e) => {
                this.updateTextShadow('opacity', e.target.value);
                if (opacityValue) {
                    opacityValue.textContent = e.target.value + '%';
                }
            });
        }

        console.log('Font controls connected:', this.fontControls);
    }
});